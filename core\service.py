"""
Background Service for FolderGuard
Monitors protected folders and handles automatic operations
"""

import threading
import time
import os
import json
import socket
import socketserver
from http.server import HTTPServer, BaseHTTPRequestHandler
from typing import Dict, List, Optional, Callable
import logging
from datetime import datetime, timedelta
import psutil
from .security import SecurityManager, BruteForceProtection
from .folder_manager import FolderManager
from .logger import SecureLogger
from .auth import SessionManager, USBKeyAuth


class FolderGuardService:
    """Main background service for FolderGuard"""
    
    def __init__(self, config_manager, database):
        self.config_manager = config_manager
        self.database = database
        self.security_manager = SecurityManager()
        self.folder_manager = FolderManager(self.security_manager)
        self.logger = SecureLogger(self.security_manager)
        self.session_manager = SessionManager()
        self.usb_auth = USBKeyAuth()
        self.brute_force_protection = BruteForceProtection()
        
        self.running = False
        self.threads = []
        self.http_server = None
        
        # Service settings
        self.check_interval = config_manager.get_config_int('Service', 'check_interval', 60)
        self.service_port = config_manager.get_config_int('Service', 'service_port', 8765)
        
        # Monitoring flags
        self.monitor_usb = config_manager.get_config_bool('Security', 'require_usb_key', False)
        self.auto_lock_enabled = True
        self.auto_lock_timeout = config_manager.get_config_int('Security', 'auto_lock_timeout', 1800)
        
        # Setup logging
        self._setup_logging()
    
    def _setup_logging(self):
        """Setup service logging"""
        log_level = self.config_manager.get_config('Logging', 'log_level', 'INFO')
        logging.basicConfig(
            level=getattr(logging, log_level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.service_logger = logging.getLogger('FolderGuardService')
    
    def start(self):
        """Start the background service"""
        if self.running:
            return
        
        self.running = True
        self.service_logger.info("Starting FolderGuard service...")
        
        # Start monitoring threads
        self._start_monitoring_threads()
        
        # Start HTTP server for remote control
        self._start_http_server()
        
        self.logger.log_system_event("SERVICE_START", "FolderGuard service started")
    
    def stop(self):
        """Stop the background service"""
        if not self.running:
            return
        
        self.running = False
        self.service_logger.info("Stopping FolderGuard service...")
        
        # Stop HTTP server
        if self.http_server:
            self.http_server.shutdown()
            self.http_server = None
        
        # Wait for threads to finish
        for thread in self.threads:
            thread.join(timeout=5)
        
        self.threads.clear()
        self.logger.log_system_event("SERVICE_STOP", "FolderGuard service stopped")
    
    def _start_monitoring_threads(self):
        """Start all monitoring threads"""
        # Folder monitoring thread
        folder_thread = threading.Thread(target=self._folder_monitor_loop, daemon=True)
        folder_thread.start()
        self.threads.append(folder_thread)
        
        # USB monitoring thread
        if self.monitor_usb:
            usb_thread = threading.Thread(target=self._usb_monitor_loop, daemon=True)
            usb_thread.start()
            self.threads.append(usb_thread)
        
        # Session cleanup thread
        session_thread = threading.Thread(target=self._session_cleanup_loop, daemon=True)
        session_thread.start()
        self.threads.append(session_thread)
        
        # Auto-lock thread
        if self.auto_lock_enabled:
            autolock_thread = threading.Thread(target=self._auto_lock_loop, daemon=True)
            autolock_thread.start()
            self.threads.append(autolock_thread)
        
        # System monitoring thread
        system_thread = threading.Thread(target=self._system_monitor_loop, daemon=True)
        system_thread.start()
        self.threads.append(system_thread)
    
    def _folder_monitor_loop(self):
        """Monitor protected folders for unauthorized access"""
        self.service_logger.info("Started folder monitoring")
        
        while self.running:
            try:
                protected_folders = self.folder_manager.get_protected_folders()
                
                for folder_id, folder_info in protected_folders.items():
                    original_path = folder_info['original_path']
                    
                    # Check if folder still exists and is accessible
                    if os.path.exists(original_path):
                        # Check for unauthorized modifications
                        self._check_folder_integrity(folder_id, folder_info)
                    else:
                        # Folder missing - potential security issue
                        self.logger.log_security_event(
                            "FOLDER_MISSING",
                            f"Protected folder missing: {original_path}"
                        )
                
                time.sleep(self.check_interval)
                
            except Exception as e:
                self.service_logger.error(f"Error in folder monitoring: {e}")
                time.sleep(30)  # Wait before retrying
    
    def _usb_monitor_loop(self):
        """Monitor USB devices for authorized keys"""
        self.service_logger.info("Started USB monitoring")
        last_usb_status = False
        
        while self.running:
            try:
                current_status = self.usb_auth.verify_usb_key()
                
                if current_status != last_usb_status:
                    if current_status:
                        self.logger.log_security_event(
                            "USB_KEY_CONNECTED",
                            "Authorized USB key detected"
                        )
                    else:
                        self.logger.log_security_event(
                            "USB_KEY_DISCONNECTED",
                            "USB key removed - initiating auto-lock"
                        )
                        # Trigger auto-lock when USB key is removed
                        self._trigger_emergency_lock()
                    
                    last_usb_status = current_status
                
                time.sleep(5)  # Check USB status every 5 seconds
                
            except Exception as e:
                self.service_logger.error(f"Error in USB monitoring: {e}")
                time.sleep(10)
    
    def _session_cleanup_loop(self):
        """Clean up expired sessions"""
        self.service_logger.info("Started session cleanup")
        
        while self.running:
            try:
                self.session_manager.cleanup_expired_sessions()
                time.sleep(300)  # Clean up every 5 minutes
                
            except Exception as e:
                self.service_logger.error(f"Error in session cleanup: {e}")
                time.sleep(60)
    
    def _auto_lock_loop(self):
        """Monitor for auto-lock conditions"""
        self.service_logger.info("Started auto-lock monitoring")
        last_activity = time.time()
        
        while self.running:
            try:
                # Check for user activity (mouse/keyboard)
                current_time = time.time()
                
                # Simple activity detection (can be enhanced)
                if self._detect_user_activity():
                    last_activity = current_time
                
                # Check if auto-lock timeout exceeded
                if current_time - last_activity > self.auto_lock_timeout:
                    self._trigger_auto_lock()
                    last_activity = current_time  # Reset to prevent repeated locks
                
                time.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                self.service_logger.error(f"Error in auto-lock monitoring: {e}")
                time.sleep(60)
    
    def _system_monitor_loop(self):
        """Monitor system resources and security events"""
        self.service_logger.info("Started system monitoring")
        
        while self.running:
            try:
                # Monitor CPU and memory usage
                cpu_percent = psutil.cpu_percent(interval=1)
                memory_percent = psutil.virtual_memory().percent
                
                # Log high resource usage
                if cpu_percent > 90:
                    self.logger.log_system_event(
                        "HIGH_CPU_USAGE",
                        f"CPU usage: {cpu_percent}%"
                    )
                
                if memory_percent > 90:
                    self.logger.log_system_event(
                        "HIGH_MEMORY_USAGE",
                        f"Memory usage: {memory_percent}%"
                    )
                
                # Monitor disk space for protected folders
                self._check_disk_space()
                
                time.sleep(60)  # Check every minute
                
            except Exception as e:
                self.service_logger.error(f"Error in system monitoring: {e}")
                time.sleep(120)
    
    def _check_folder_integrity(self, folder_id: str, folder_info: Dict):
        """Check integrity of protected folder"""
        try:
            original_path = folder_info['original_path']
            
            # Check if folder is still hidden (if it should be)
            if folder_info.get('hidden', False):
                if os.path.exists(original_path) and not self._is_folder_hidden(original_path):
                    self.logger.log_security_event(
                        "FOLDER_UNHIDDEN",
                        f"Protected folder no longer hidden: {original_path}"
                    )
            
            # Check for unauthorized access attempts
            # This could include checking file modification times, access logs, etc.
            
        except Exception as e:
            self.service_logger.error(f"Error checking folder integrity: {e}")
    
    def _is_folder_hidden(self, folder_path: str) -> bool:
        """Check if folder is hidden"""
        try:
            if os.name == 'nt':  # Windows
                import subprocess
                result = subprocess.run(
                    ['attrib', folder_path],
                    capture_output=True,
                    text=True
                )
                return 'H' in result.stdout
            else:  # Unix-like
                return os.path.basename(folder_path).startswith('.')
        except Exception:
            return False
    
    def _detect_user_activity(self) -> bool:
        """Detect user activity (simplified implementation)"""
        try:
            # This is a simplified implementation
            # In a real application, you might use more sophisticated methods
            # to detect mouse movement, keyboard input, etc.
            
            # For now, just check if there are active user processes
            for proc in psutil.process_iter(['pid', 'name', 'username']):
                try:
                    if proc.info['name'] in ['explorer.exe', 'dwm.exe', 'winlogon.exe']:
                        return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            return False
        except Exception:
            return True  # Assume activity if we can't detect
    
    def _trigger_auto_lock(self):
        """Trigger automatic lock of all protected folders"""
        try:
            self.logger.log_security_event(
                "AUTO_LOCK_TRIGGERED",
                "Auto-lock activated due to inactivity"
            )
            
            # Lock all protected folders
            # Implementation depends on your locking mechanism
            
        except Exception as e:
            self.service_logger.error(f"Error triggering auto-lock: {e}")
    
    def _trigger_emergency_lock(self):
        """Trigger emergency lock (e.g., when USB key is removed)"""
        try:
            self.logger.log_security_event(
                "EMERGENCY_LOCK_TRIGGERED",
                "Emergency lock activated"
            )
            
            # Immediately lock all protected folders
            # This should be faster than normal auto-lock
            
        except Exception as e:
            self.service_logger.error(f"Error triggering emergency lock: {e}")
    
    def _check_disk_space(self):
        """Check disk space for protected folder locations"""
        try:
            protected_folders = self.folder_manager.get_protected_folders()
            
            for folder_info in protected_folders.values():
                folder_path = folder_info['original_path']
                
                # Get disk usage for the drive containing the folder
                disk_usage = psutil.disk_usage(os.path.dirname(folder_path))
                free_percent = (disk_usage.free / disk_usage.total) * 100
                
                if free_percent < 10:  # Less than 10% free space
                    self.logger.log_system_event(
                        "LOW_DISK_SPACE",
                        f"Low disk space on {folder_path}: {free_percent:.1f}% free"
                    )
                
        except Exception as e:
            self.service_logger.error(f"Error checking disk space: {e}")
    
    def _start_http_server(self):
        """Start HTTP server for remote control"""
        try:
            handler = self._create_http_handler()
            self.http_server = HTTPServer(('localhost', self.service_port), handler)
            
            server_thread = threading.Thread(
                target=self.http_server.serve_forever,
                daemon=True
            )
            server_thread.start()
            self.threads.append(server_thread)
            
            self.service_logger.info(f"HTTP server started on port {self.service_port}")
            
        except Exception as e:
            self.service_logger.error(f"Error starting HTTP server: {e}")
    
    def _create_http_handler(self):
        """Create HTTP request handler class"""
        service = self
        
        class ServiceHTTPHandler(BaseHTTPRequestHandler):
            def do_GET(self):
                if self.path == '/status':
                    self._handle_status()
                elif self.path == '/folders':
                    self._handle_folders()
                else:
                    self.send_error(404)
            
            def do_POST(self):
                if self.path == '/lock':
                    self._handle_lock()
                elif self.path == '/unlock':
                    self._handle_unlock()
                else:
                    self.send_error(404)
            
            def _handle_status(self):
                status = {
                    'running': service.running,
                    'protected_folders': len(service.folder_manager.get_protected_folders()),
                    'active_sessions': len(service.session_manager.active_sessions),
                    'uptime': time.time() - service.start_time if hasattr(service, 'start_time') else 0
                }
                
                self.send_response(200)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps(status).encode())
            
            def _handle_folders(self):
                folders = service.folder_manager.get_protected_folders()
                
                self.send_response(200)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps(folders, default=str).encode())
            
            def _handle_lock(self):
                # Handle lock request
                service._trigger_emergency_lock()
                
                self.send_response(200)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({'status': 'locked'}).encode())
            
            def _handle_unlock(self):
                # Handle unlock request (requires authentication)
                self.send_response(401)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({'error': 'Authentication required'}).encode())
            
            def log_message(self, format, *args):
                # Suppress default HTTP logging
                pass
        
        return ServiceHTTPHandler
    
    def get_status(self) -> Dict:
        """Get service status information"""
        return {
            'running': self.running,
            'protected_folders': len(self.folder_manager.get_protected_folders()),
            'active_sessions': len(self.session_manager.active_sessions),
            'threads': len(self.threads),
            'usb_monitoring': self.monitor_usb,
            'auto_lock_enabled': self.auto_lock_enabled
        }
