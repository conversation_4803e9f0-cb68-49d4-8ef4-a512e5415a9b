"""
Encrypted SQLite Database Manager
Handles user data, settings, and security information
"""

import sqlite3
import json
import os
from typing import Dict, List, Optional, Any
from datetime import datetime
import threading


class SecureDatabase:
    """Encrypted SQLite database for storing application data"""
    
    def __init__(self, db_path: str = "folderguard.db"):
        self.db_path = db_path
        self.connection = None
        self.lock = threading.Lock()
        self._initialize_database()
    
    def _initialize_database(self):
        """Initialize database with required tables"""
        with self.lock:
            self.connection = sqlite3.connect(self.db_path, check_same_thread=False)
            self.connection.row_factory = sqlite3.Row
            
            # Enable WAL mode for better concurrency
            self.connection.execute("PRAGMA journal_mode=WAL")
            
            self._create_tables()
    
    def _create_tables(self):
        """Create database tables"""
        cursor = self.connection.cursor()
        
        # Users table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE,
                password_hash TEXT NOT NULL,
                totp_secret TEXT,
                backup_codes TEXT,
                usb_key_id TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP,
                is_active BOOLEAN DEFAULT 1,
                failed_attempts INTEGER DEFAULT 0,
                locked_until TIMESTAMP
            )
        """)
        
        # Settings table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                setting_key TEXT NOT NULL,
                setting_value TEXT,
                encrypted BOOLEAN DEFAULT 0,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id),
                UNIQUE(user_id, setting_key)
            )
        """)
        
        # Protected folders table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS protected_folders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                folder_id TEXT UNIQUE NOT NULL,
                original_path TEXT NOT NULL,
                archive_path TEXT NOT NULL,
                is_hidden BOOLEAN DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_accessed TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)
        
        # Sessions table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT UNIQUE NOT NULL,
                user_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                ip_address TEXT,
                user_agent TEXT,
                is_active BOOLEAN DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)
        
        # Security events table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS security_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                event_type TEXT NOT NULL,
                event_data TEXT,
                ip_address TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)
        
        # USB keys table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS usb_keys (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key_id TEXT UNIQUE NOT NULL,
                user_id INTEGER,
                key_name TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_used TIMESTAMP,
                is_active BOOLEAN DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)
        
        self.connection.commit()
    
    def create_user(self, username: str, email: str, password_hash: str, 
                   totp_secret: str = None) -> Optional[int]:
        """Create a new user"""
        try:
            with self.lock:
                cursor = self.connection.cursor()
                cursor.execute("""
                    INSERT INTO users (username, email, password_hash, totp_secret)
                    VALUES (?, ?, ?, ?)
                """, (username, email, password_hash, totp_secret))
                
                user_id = cursor.lastrowid
                self.connection.commit()
                return user_id
        except sqlite3.IntegrityError:
            return None
        except Exception as e:
            print(f"Error creating user: {e}")
            return None
    
    def get_user(self, username: str = None, user_id: int = None) -> Optional[Dict]:
        """Get user by username or ID"""
        try:
            with self.lock:
                cursor = self.connection.cursor()
                
                if username:
                    cursor.execute("SELECT * FROM users WHERE username = ?", (username,))
                elif user_id:
                    cursor.execute("SELECT * FROM users WHERE id = ?", (user_id,))
                else:
                    return None
                
                row = cursor.fetchone()
                return dict(row) if row else None
        except Exception as e:
            print(f"Error getting user: {e}")
            return None
    
    def update_user(self, user_id: int, **kwargs) -> bool:
        """Update user information"""
        try:
            if not kwargs:
                return True
            
            with self.lock:
                cursor = self.connection.cursor()
                
                # Build dynamic update query
                set_clause = ", ".join([f"{key} = ?" for key in kwargs.keys()])
                values = list(kwargs.values()) + [user_id]
                
                cursor.execute(f"""
                    UPDATE users SET {set_clause} WHERE id = ?
                """, values)
                
                self.connection.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"Error updating user: {e}")
            return False
    
    def record_failed_login(self, username: str) -> int:
        """Record failed login attempt and return current count"""
        try:
            with self.lock:
                cursor = self.connection.cursor()
                
                # Get current failed attempts
                cursor.execute("""
                    SELECT failed_attempts FROM users WHERE username = ?
                """, (username,))
                
                row = cursor.fetchone()
                if not row:
                    return 0
                
                failed_attempts = row[0] + 1
                
                # Update failed attempts
                cursor.execute("""
                    UPDATE users SET failed_attempts = ? WHERE username = ?
                """, (failed_attempts, username))
                
                self.connection.commit()
                return failed_attempts
        except Exception as e:
            print(f"Error recording failed login: {e}")
            return 0
    
    def reset_failed_attempts(self, username: str) -> bool:
        """Reset failed login attempts"""
        try:
            with self.lock:
                cursor = self.connection.cursor()
                cursor.execute("""
                    UPDATE users SET failed_attempts = 0, locked_until = NULL 
                    WHERE username = ?
                """, (username,))
                
                self.connection.commit()
                return True
        except Exception as e:
            print(f"Error resetting failed attempts: {e}")
            return False
    
    def lock_user(self, username: str, lock_duration: int = 300) -> bool:
        """Lock user account for specified duration (seconds)"""
        try:
            from datetime import timedelta
            
            lock_until = datetime.now() + timedelta(seconds=lock_duration)
            
            with self.lock:
                cursor = self.connection.cursor()
                cursor.execute("""
                    UPDATE users SET locked_until = ? WHERE username = ?
                """, (lock_until, username))
                
                self.connection.commit()
                return True
        except Exception as e:
            print(f"Error locking user: {e}")
            return False
    
    def is_user_locked(self, username: str) -> bool:
        """Check if user is currently locked"""
        try:
            with self.lock:
                cursor = self.connection.cursor()
                cursor.execute("""
                    SELECT locked_until FROM users WHERE username = ?
                """, (username,))
                
                row = cursor.fetchone()
                if not row or not row[0]:
                    return False
                
                lock_until = datetime.fromisoformat(row[0])
                return datetime.now() < lock_until
        except Exception as e:
            print(f"Error checking user lock: {e}")
            return False
    
    def save_setting(self, user_id: int, key: str, value: Any, encrypted: bool = False) -> bool:
        """Save user setting"""
        try:
            value_str = json.dumps(value) if not isinstance(value, str) else value
            
            with self.lock:
                cursor = self.connection.cursor()
                cursor.execute("""
                    INSERT OR REPLACE INTO settings 
                    (user_id, setting_key, setting_value, encrypted, updated_at)
                    VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
                """, (user_id, key, value_str, encrypted))
                
                self.connection.commit()
                return True
        except Exception as e:
            print(f"Error saving setting: {e}")
            return False
    
    def get_setting(self, user_id: int, key: str, default: Any = None) -> Any:
        """Get user setting"""
        try:
            with self.lock:
                cursor = self.connection.cursor()
                cursor.execute("""
                    SELECT setting_value, encrypted FROM settings 
                    WHERE user_id = ? AND setting_key = ?
                """, (user_id, key))
                
                row = cursor.fetchone()
                if not row:
                    return default
                
                value_str = row[0]
                try:
                    return json.loads(value_str)
                except json.JSONDecodeError:
                    return value_str
        except Exception as e:
            print(f"Error getting setting: {e}")
            return default
    
    def add_protected_folder(self, user_id: int, folder_id: str, original_path: str,
                           archive_path: str, is_hidden: bool = False) -> bool:
        """Add protected folder record"""
        try:
            with self.lock:
                cursor = self.connection.cursor()
                cursor.execute("""
                    INSERT INTO protected_folders 
                    (user_id, folder_id, original_path, archive_path, is_hidden)
                    VALUES (?, ?, ?, ?, ?)
                """, (user_id, folder_id, original_path, archive_path, is_hidden))
                
                self.connection.commit()
                return True
        except Exception as e:
            print(f"Error adding protected folder: {e}")
            return False
    
    def remove_protected_folder(self, folder_id: str) -> bool:
        """Remove protected folder record"""
        try:
            with self.lock:
                cursor = self.connection.cursor()
                cursor.execute("""
                    DELETE FROM protected_folders WHERE folder_id = ?
                """, (folder_id,))
                
                self.connection.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"Error removing protected folder: {e}")
            return False
    
    def get_protected_folders(self, user_id: int) -> List[Dict]:
        """Get user's protected folders"""
        try:
            with self.lock:
                cursor = self.connection.cursor()
                cursor.execute("""
                    SELECT * FROM protected_folders WHERE user_id = ?
                    ORDER BY created_at DESC
                """, (user_id,))
                
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"Error getting protected folders: {e}")
            return []
    
    def close(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
