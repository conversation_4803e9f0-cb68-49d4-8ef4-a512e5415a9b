"""
Two-Factor Authentication Module
Implements TOTP-based 2FA with QR code generation
"""

import pyotp
import qrcode
import io
import base64
from PIL import Image
from typing import Optional, Tuple
import secrets
import time


class TwoFactorAuth:
    """Two-Factor Authentication manager using TOTP"""
    
    def __init__(self, app_name: str = "FolderGuard"):
        self.app_name = app_name
        self.issuer = "SecureFolder"
    
    def generate_secret(self) -> str:
        """Generate a new TOTP secret"""
        return pyotp.random_base32()
    
    def generate_qr_code(self, secret: str, user_email: str) -> bytes:
        """Generate QR code for TOTP setup"""
        totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
            name=user_email,
            issuer_name=self.issuer
        )
        
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(totp_uri)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        
        # Convert to bytes
        img_buffer = io.BytesIO()
        img.save(img_buffer, format='PNG')
        return img_buffer.getvalue()
    
    def verify_token(self, secret: str, token: str, window: int = 1) -> bool:
        """Verify TOTP token with time window tolerance"""
        try:
            totp = pyotp.TOTP(secret)
            return totp.verify(token, valid_window=window)
        except Exception:
            return False
    
    def get_current_token(self, secret: str) -> str:
        """Get current TOTP token (for testing)"""
        totp = pyotp.TOTP(secret)
        return totp.now()
    
    def get_backup_codes(self, count: int = 10) -> list:
        """Generate backup codes for 2FA recovery"""
        codes = []
        for _ in range(count):
            code = secrets.token_hex(4).upper()
            codes.append(f"{code[:4]}-{code[4:]}")
        return codes


class SessionManager:
    """Manage user sessions with timeout"""
    
    def __init__(self, session_timeout: int = 3600):  # 1 hour default
        self.session_timeout = session_timeout
        self.active_sessions = {}
        self.session_data = {}
    
    def create_session(self, user_id: str) -> str:
        """Create a new session"""
        session_id = secrets.token_urlsafe(32)
        current_time = time.time()
        
        self.active_sessions[session_id] = {
            'user_id': user_id,
            'created_at': current_time,
            'last_activity': current_time
        }
        
        return session_id
    
    def validate_session(self, session_id: str) -> bool:
        """Validate if session is active and not expired"""
        if session_id not in self.active_sessions:
            return False
        
        session = self.active_sessions[session_id]
        current_time = time.time()
        
        # Check if session expired
        if current_time - session['last_activity'] > self.session_timeout:
            self.destroy_session(session_id)
            return False
        
        # Update last activity
        session['last_activity'] = current_time
        return True
    
    def destroy_session(self, session_id: str):
        """Destroy a session"""
        if session_id in self.active_sessions:
            del self.active_sessions[session_id]
        if session_id in self.session_data:
            del self.session_data[session_id]
    
    def get_session_user(self, session_id: str) -> Optional[str]:
        """Get user ID from session"""
        if self.validate_session(session_id):
            return self.active_sessions[session_id]['user_id']
        return None
    
    def cleanup_expired_sessions(self):
        """Remove expired sessions"""
        current_time = time.time()
        expired_sessions = []
        
        for session_id, session in self.active_sessions.items():
            if current_time - session['last_activity'] > self.session_timeout:
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            self.destroy_session(session_id)


class USBKeyAuth:
    """USB Key authentication for additional security"""
    
    def __init__(self):
        self.usb_drives = []
        self.authorized_keys = set()
    
    def scan_usb_drives(self) -> list:
        """Scan for available USB drives"""
        import psutil
        drives = []
        
        for partition in psutil.disk_partitions():
            if 'removable' in partition.opts:
                drives.append({
                    'device': partition.device,
                    'mountpoint': partition.mountpoint,
                    'fstype': partition.fstype
                })
        
        return drives
    
    def create_usb_key(self, drive_path: str, key_name: str = "folder_guard_key") -> str:
        """Create USB key file with unique identifier"""
        import os
        import json
        
        key_id = secrets.token_hex(16)
        key_data = {
            'app': 'FolderGuard',
            'key_id': key_id,
            'created_at': time.time(),
            'name': key_name
        }
        
        key_file_path = os.path.join(drive_path, f"{key_name}.fgkey")
        
        try:
            with open(key_file_path, 'w') as f:
                json.dump(key_data, f)
            
            self.authorized_keys.add(key_id)
            return key_id
        except Exception as e:
            print(f"Error creating USB key: {e}")
            return None
    
    def verify_usb_key(self, key_id: str = None) -> bool:
        """Verify if authorized USB key is present"""
        import os
        import json
        
        drives = self.scan_usb_drives()
        
        for drive in drives:
            try:
                # Look for key files
                for file in os.listdir(drive['mountpoint']):
                    if file.endswith('.fgkey'):
                        key_file_path = os.path.join(drive['mountpoint'], file)
                        
                        with open(key_file_path, 'r') as f:
                            key_data = json.load(f)
                        
                        if key_data.get('app') == 'FolderGuard':
                            found_key_id = key_data.get('key_id')
                            
                            if key_id is None:
                                # Any authorized key is acceptable
                                if found_key_id in self.authorized_keys:
                                    return True
                            else:
                                # Specific key required
                                if found_key_id == key_id:
                                    return True
            except Exception:
                continue
        
        return False
    
    def add_authorized_key(self, key_id: str):
        """Add key ID to authorized keys"""
        self.authorized_keys.add(key_id)
    
    def remove_authorized_key(self, key_id: str):
        """Remove key ID from authorized keys"""
        self.authorized_keys.discard(key_id)
