"""
Configuration Management
Handles application settings and configuration
"""

import os
import json
import configparser
from typing import Dict, Any, Optional
from pathlib import Path


class ConfigManager:
    """Application configuration manager"""

    def __init__(self, config_dir: str = None):
        if config_dir is None:
            # Use user's app data directory
            if os.name == 'nt':  # Windows
                config_dir = os.path.join(os.environ.get('APPDATA', ''), 'FolderGuard')
            else:  # Unix-like
                config_dir = os.path.join(os.path.expanduser('~'), '.folderguard')

        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)

        self.config_file = self.config_dir / 'config.ini'
        self.settings_file = self.config_dir / 'settings.json'

        self.config = configparser.ConfigParser()
        self.settings = {}

        self._load_config()
        self._load_settings()

    def _load_config(self):
        """Load configuration from INI file"""
        if self.config_file.exists():
            self.config.read(self.config_file)
        else:
            self._create_default_config()

    def _create_default_config(self):
        """Create default configuration"""
        self.config['Security'] = {
            'max_login_attempts': '5',
            'lockout_duration': '300',
            'session_timeout': '3600',
            'require_2fa': 'true',
            'require_usb_key': 'false',
            'auto_lock_timeout': '1800'
        }

        self.config['Encryption'] = {
            'algorithm': 'AES-256-CBC',
            'key_iterations': '100000',
            'salt_length': '32',
            'iv_length': '16'
        }

        self.config['Logging'] = {
            'log_level': 'INFO',
            'max_log_size': '10485760',  # 10MB
            'log_retention_days': '30',
            'encrypt_logs': 'true'
        }

        self.config['GUI'] = {
            'theme': 'dark',
            'language': 'en',
            'window_width': '1200',
            'window_height': '800',
            'remember_window_size': 'true',
            'minimize_to_tray': 'true'
        }

        self.config['Folders'] = {
            'default_protection_level': 'high',
            'auto_hide_protected': 'true',
            'backup_before_protect': 'true',
            'temp_directory': ''
        }

        self._save_config()

    def _save_config(self):
        """Save configuration to file"""
        with open(self.config_file, 'w') as f:
            self.config.write(f)

    def _load_settings(self):
        """Load user settings from JSON file"""
        if self.settings_file.exists():
            try:
                with open(self.settings_file, 'r') as f:
                    self.settings = json.load(f)
            except Exception as e:
                print(f"Error loading settings: {e}")
                self.settings = {}

    def _save_settings(self):
        """Save user settings to JSON file"""
        try:
            with open(self.settings_file, 'w') as f:
                json.dump(self.settings, f, indent=2)
        except Exception as e:
            print(f"Error saving settings: {e}")

    def get_config(self, section: str, key: str, fallback: str = None) -> str:
        """Get configuration value"""
        return self.config.get(section, key, fallback=fallback)

    def get_config_int(self, section: str, key: str, fallback: int = 0) -> int:
        """Get configuration value as integer"""
        return self.config.getint(section, key, fallback=fallback)

    def get_config_bool(self, section: str, key: str, fallback: bool = False) -> bool:
        """Get configuration value as boolean"""
        return self.config.getboolean(section, key, fallback=fallback)

    def set_config(self, section: str, key: str, value: str):
        """Set configuration value"""
        if section not in self.config:
            self.config.add_section(section)

        self.config.set(section, key, str(value))
        self._save_config()

    def get_setting(self, key: str, default: Any = None) -> Any:
        """Get user setting"""
        return self.settings.get(key, default)

    def set_setting(self, key: str, value: Any):
        """Set user setting"""
        self.settings[key] = value
        self._save_settings()

    def get_security_settings(self) -> Dict[str, Any]:
        """Get security-related settings"""
        return {
            'max_login_attempts': self.get_config_int('Security', 'max_login_attempts', 5),
            'lockout_duration': self.get_config_int('Security', 'lockout_duration', 300),
            'session_timeout': self.get_config_int('Security', 'session_timeout', 3600),
            'require_2fa': self.get_config_bool('Security', 'require_2fa', True),
            'require_usb_key': self.get_config_bool('Security', 'require_usb_key', False),
            'auto_lock_timeout': self.get_config_int('Security', 'auto_lock_timeout', 1800)
        }

    def get_gui_settings(self) -> Dict[str, Any]:
        """Get GUI-related settings"""
        return {
            'theme': self.get_config('GUI', 'theme', 'dark'),
            'language': self.get_config('GUI', 'language', 'en'),
            'window_width': self.get_config_int('GUI', 'window_width', 1200),
            'window_height': self.get_config_int('GUI', 'window_height', 800),
            'remember_window_size': self.get_config_bool('GUI', 'remember_window_size', True),
            'minimize_to_tray': self.get_config_bool('GUI', 'minimize_to_tray', True)
        }

    def get_data_directory(self) -> Path:
        """Get application data directory"""
        return self.config_dir

    def get_log_directory(self) -> Path:
        """Get log directory"""
        log_dir = self.config_dir / 'logs'
        log_dir.mkdir(exist_ok=True)
        return log_dir
