"""
Main Application Window
Modern GUI with drag-and-drop support and folder management
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import filedialog, messagebox
import os
from typing import List, Dict, Optional
import threading
from .styles import ModernStyles, DARK_THEME, FONTS, IconManager
from .login_dialog import LoginDialog
import tkinterdnd2 as tkdnd


class MainWindow(ctk.CTk):
    """Main application window"""
    
    def __init__(self):
        super().__init__()
        
        self.current_user = None
        self.protected_folders = {}
        self.selected_folders = set()
        
        self._setup_window()
        self._create_widgets()
        self._setup_drag_drop()
        self._setup_bindings()
        
        # Show login dialog
        self._show_login()
    
    def _setup_window(self):
        """Setup main window properties"""
        self.title("FolderGuard - حماية متقدمة للمجلدات")
        self.geometry("1200x800")
        self.minsize(800, 600)
        
        # Configure grid
        self.grid_columnconfigure(1, weight=1)
        self.grid_rowconfigure(0, weight=1)
        
        # Apply theme
        ModernStyles.configure_theme()
        
        # Set window icon (if available)
        try:
            self.iconbitmap("assets/icon.ico")
        except:
            pass
    
    def _create_widgets(self):
        """Create and layout main widgets"""
        # Sidebar
        self._create_sidebar()
        
        # Main content area
        self._create_main_content()
        
        # Status bar
        self._create_status_bar()
    
    def _create_sidebar(self):
        """Create sidebar with navigation and controls"""
        self.sidebar = ctk.CTkFrame(self, **ModernStyles.get_frame_style('sidebar'))
        self.sidebar.grid(row=0, column=0, sticky="nsew", padx=(10, 5), pady=10)
        self.sidebar.grid_rowconfigure(8, weight=1)  # Spacer
        
        # Logo/Title
        logo_label = ctk.CTkLabel(
            self.sidebar,
            text="🔒 FolderGuard",
            **ModernStyles.get_label_style('title')
        )
        logo_label.grid(row=0, column=0, pady=(20, 30), padx=20)
        
        # User info
        self.user_frame = ctk.CTkFrame(self.sidebar, **ModernStyles.get_frame_style('card'))
        self.user_frame.grid(row=1, column=0, sticky="ew", padx=15, pady=(0, 20))
        
        self.user_label = ctk.CTkLabel(
            self.user_frame,
            text="مرحباً، المستخدم",
            **ModernStyles.get_label_style('subheading')
        )
        self.user_label.grid(row=0, column=0, pady=10, padx=15)
        
        # Navigation buttons
        nav_buttons = [
            ("📁 إدارة المجلدات", self._show_folder_management),
            ("🔐 المجلدات المحمية", self._show_protected_folders),
            ("📊 سجل الأنشطة", self._show_activity_log),
            ("⚙️ الإعدادات", self._show_settings),
            ("🔑 إدارة المفاتيح", self._show_key_management),
        ]
        
        self.nav_buttons = {}
        for i, (text, command) in enumerate(nav_buttons):
            btn = ctk.CTkButton(
                self.sidebar,
                text=text,
                command=command,
                **ModernStyles.get_button_style('secondary'),
                anchor="w"
            )
            btn.grid(row=i+2, column=0, sticky="ew", padx=15, pady=5)
            self.nav_buttons[text] = btn
        
        # Quick actions
        quick_frame = ctk.CTkFrame(self.sidebar, **ModernStyles.get_frame_style('card'))
        quick_frame.grid(row=9, column=0, sticky="ew", padx=15, pady=20)
        
        quick_label = ctk.CTkLabel(
            quick_frame,
            text="إجراءات سريعة",
            **ModernStyles.get_label_style('subheading')
        )
        quick_label.grid(row=0, column=0, pady=(10, 5), padx=15)
        
        protect_btn = ctk.CTkButton(
            quick_frame,
            text="🛡️ حماية مجلد",
            command=self._quick_protect_folder,
            **ModernStyles.get_button_style('primary')
        )
        protect_btn.grid(row=1, column=0, sticky="ew", padx=15, pady=5)
        
        unprotect_btn = ctk.CTkButton(
            quick_frame,
            text="🔓 إلغاء الحماية",
            command=self._quick_unprotect_folder,
            **ModernStyles.get_button_style('warning')
        )
        unprotect_btn.grid(row=2, column=0, sticky="ew", padx=15, pady=(5, 15))
        
        # Logout button
        logout_btn = ctk.CTkButton(
            self.sidebar,
            text="🚪 تسجيل الخروج",
            command=self._logout,
            **ModernStyles.get_button_style('danger')
        )
        logout_btn.grid(row=10, column=0, sticky="ew", padx=15, pady=(0, 20))
    
    def _create_main_content(self):
        """Create main content area"""
        self.main_frame = ctk.CTkFrame(self, **ModernStyles.get_frame_style('card'))
        self.main_frame.grid(row=0, column=1, sticky="nsew", padx=(5, 10), pady=10)
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_rowconfigure(1, weight=1)
        
        # Header
        self.header_frame = ctk.CTkFrame(self.main_frame, fg_color="transparent")
        self.header_frame.grid(row=0, column=0, sticky="ew", padx=20, pady=(20, 10))
        self.header_frame.grid_columnconfigure(1, weight=1)
        
        self.page_title = ctk.CTkLabel(
            self.header_frame,
            text="إدارة المجلدات",
            **ModernStyles.get_label_style('heading')
        )
        self.page_title.grid(row=0, column=0, sticky="w")
        
        # Search frame
        self.search_frame = ctk.CTkFrame(self.header_frame, fg_color="transparent")
        self.search_frame.grid(row=0, column=1, sticky="e")
        
        self.search_entry = ctk.CTkEntry(
            self.search_frame,
            placeholder_text="🔍 البحث في المجلدات...",
            width=250,
            **ModernStyles.get_entry_style()
        )
        self.search_entry.grid(row=0, column=0, padx=(0, 10))
        
        self.refresh_btn = ctk.CTkButton(
            self.search_frame,
            text="🔄",
            width=40,
            command=self._refresh_content,
            **ModernStyles.get_button_style('secondary')
        )
        self.refresh_btn.grid(row=0, column=1)
        
        # Content area (will be replaced by different views)
        self.content_frame = ctk.CTkFrame(self.main_frame, fg_color="transparent")
        self.content_frame.grid(row=1, column=0, sticky="nsew", padx=20, pady=(10, 20))
        self.content_frame.grid_columnconfigure(0, weight=1)
        self.content_frame.grid_rowconfigure(0, weight=1)
        
        # Default view - folder management
        self._create_folder_management_view()
    
    def _create_status_bar(self):
        """Create status bar"""
        self.status_frame = ctk.CTkFrame(self, height=30, **ModernStyles.get_frame_style('card'))
        self.status_frame.grid(row=1, column=0, columnspan=2, sticky="ew", padx=10, pady=(0, 10))
        self.status_frame.grid_columnconfigure(1, weight=1)
        
        # Status indicator
        self.status_indicator = ctk.CTkLabel(
            self.status_frame,
            text="🟢",
            font=FONTS['small']
        )
        self.status_indicator.grid(row=0, column=0, padx=(15, 5), pady=5)
        
        # Status text
        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="جاهز",
            **ModernStyles.get_label_style('small')
        )
        self.status_label.grid(row=0, column=1, sticky="w", pady=5)
        
        # Connection status
        self.connection_label = ctk.CTkLabel(
            self.status_frame,
            text="متصل",
            **ModernStyles.get_label_style('small')
        )
        self.connection_label.grid(row=0, column=2, padx=(0, 15), pady=5)
    
    def _create_folder_management_view(self):
        """Create folder management view"""
        # Clear content frame
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        
        # Drop zone
        self.drop_zone = ctk.CTkFrame(
            self.content_frame,
            height=150,
            **ModernStyles.get_frame_style('card')
        )
        self.drop_zone.grid(row=0, column=0, sticky="ew", pady=(0, 20))
        self.drop_zone.grid_columnconfigure(0, weight=1)
        self.drop_zone.grid_propagate(False)
        
        drop_label = ctk.CTkLabel(
            self.drop_zone,
            text="📁 اسحب المجلدات هنا لحمايتها\nأو انقر لاختيار مجلد",
            **ModernStyles.get_label_style('heading'),
            justify="center"
        )
        drop_label.grid(row=0, column=0, pady=50)
        
        # Make drop zone clickable
        drop_label.bind("<Button-1>", lambda e: self._select_folder())
        self.drop_zone.bind("<Button-1>", lambda e: self._select_folder())
        
        # Folder list
        self.folder_list_frame = ctk.CTkScrollableFrame(
            self.content_frame,
            **ModernStyles.get_frame_style('card')
        )
        self.folder_list_frame.grid(row=1, column=0, sticky="nsew")
        self.folder_list_frame.grid_columnconfigure(0, weight=1)
        
        # List header
        header_frame = ctk.CTkFrame(self.folder_list_frame, fg_color="transparent")
        header_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        header_frame.grid_columnconfigure(1, weight=1)
        
        ctk.CTkLabel(
            header_frame,
            text="المجلدات المحمية",
            **ModernStyles.get_label_style('subheading')
        ).grid(row=0, column=0, sticky="w")
        
        # Refresh protected folders list
        self._refresh_protected_folders()
    
    def _setup_drag_drop(self):
        """Setup drag and drop functionality"""
        try:
            # Enable drag and drop on drop zone
            self.drop_zone.drop_target_register(tkdnd.DND_FILES)
            self.drop_zone.dnd_bind('<<Drop>>', self._handle_drop)
        except Exception as e:
            print(f"Drag and drop not available: {e}")
    
    def _setup_bindings(self):
        """Setup keyboard and event bindings"""
        # Keyboard shortcuts
        self.bind('<Control-o>', lambda e: self._select_folder())
        self.bind('<Control-r>', lambda e: self._refresh_content())
        self.bind('<Control-q>', lambda e: self._logout())
        self.bind('<F5>', lambda e: self._refresh_content())
        
        # Search binding
        self.search_entry.bind('<KeyRelease>', self._on_search)
    
    def _show_login(self):
        """Show login dialog"""
        login_dialog = LoginDialog(self, self._authenticate_user)
        self.wait_window(login_dialog)
        
        if login_dialog.result and login_dialog.result.get('success'):
            self.current_user = login_dialog.result
            self._update_user_info()
        else:
            self.destroy()
    
    def _authenticate_user(self, username: str, password: str, totp_code: str = "") -> dict:
        """Authenticate user (mock implementation)"""
        # Mock authentication - replace with real implementation
        import time
        time.sleep(1)  # Simulate network delay
        
        if username == "admin" and password == "admin":
            return {
                'success': True,
                'user_id': 1,
                'username': username,
                'require_2fa': bool(totp_code == "")
            }
        else:
            return {
                'success': False,
                'error': 'اسم المستخدم أو كلمة المرور غير صحيحة'
            }
    
    def _update_user_info(self):
        """Update user information display"""
        if self.current_user:
            username = self.current_user.get('username', 'مستخدم')
            self.user_label.configure(text=f"مرحباً، {username}")
    
    def _handle_drop(self, event):
        """Handle drag and drop of folders"""
        files = event.data.split()
        folders = [f.strip('{}') for f in files if os.path.isdir(f.strip('{}'))]
        
        if folders:
            self._protect_folders(folders)
        else:
            self.update_status("يرجى سحب مجلدات فقط", "warning")
    
    def _select_folder(self):
        """Open folder selection dialog"""
        folder = filedialog.askdirectory(title="اختر مجلد للحماية")
        if folder:
            self._protect_folders([folder])
    
    def _protect_folders(self, folders: List[str]):
        """Protect selected folders"""
        if not folders:
            return
        
        # Show protection dialog
        from .protection_dialog import ProtectionDialog
        dialog = ProtectionDialog(self, folders)
        self.wait_window(dialog)
        
        if dialog.result:
            self._refresh_protected_folders()
            self.update_status(f"تم حماية {len(folders)} مجلد بنجاح", "success")
    
    def _refresh_protected_folders(self):
        """Refresh the list of protected folders"""
        # Clear existing items
        for widget in self.folder_list_frame.winfo_children()[1:]:  # Skip header
            widget.destroy()
        
        # Mock data - replace with real data
        protected_folders = [
            {"name": "المستندات المهمة", "path": "C:/Important", "status": "محمي", "date": "2024-01-15"},
            {"name": "الصور الشخصية", "path": "C:/Photos", "status": "مخفي", "date": "2024-01-14"},
            {"name": "ملفات العمل", "path": "C:/Work", "status": "محمي", "date": "2024-01-13"},
        ]
        
        for i, folder in enumerate(protected_folders):
            self._create_folder_item(folder, i + 1)
    
    def _create_folder_item(self, folder_data: dict, row: int):
        """Create a folder item in the list"""
        item_frame = ctk.CTkFrame(self.folder_list_frame, **ModernStyles.get_frame_style('card'))
        item_frame.grid(row=row, column=0, sticky="ew", pady=5)
        item_frame.grid_columnconfigure(1, weight=1)
        
        # Folder icon and name
        info_frame = ctk.CTkFrame(item_frame, fg_color="transparent")
        info_frame.grid(row=0, column=0, sticky="w", padx=15, pady=10)
        
        icon_label = ctk.CTkLabel(info_frame, text="📁", font=FONTS['heading'])
        icon_label.grid(row=0, column=0, padx=(0, 10))
        
        name_label = ctk.CTkLabel(
            info_frame,
            text=folder_data['name'],
            **ModernStyles.get_label_style('subheading')
        )
        name_label.grid(row=0, column=1)
        
        path_label = ctk.CTkLabel(
            info_frame,
            text=folder_data['path'],
            **ModernStyles.get_label_style('small')
        )
        path_label.grid(row=1, column=1, sticky="w")
        
        # Status and date
        status_frame = ctk.CTkFrame(item_frame, fg_color="transparent")
        status_frame.grid(row=0, column=1, sticky="e", padx=15, pady=10)
        
        status_color = DARK_THEME['success_color'] if folder_data['status'] == 'محمي' else DARK_THEME['warning_color']
        status_label = ctk.CTkLabel(
            status_frame,
            text=f"🔒 {folder_data['status']}",
            text_color=status_color,
            **ModernStyles.get_label_style('small')
        )
        status_label.grid(row=0, column=0, sticky="e")
        
        date_label = ctk.CTkLabel(
            status_frame,
            text=folder_data['date'],
            **ModernStyles.get_label_style('small')
        )
        date_label.grid(row=1, column=0, sticky="e")
        
        # Action buttons
        action_frame = ctk.CTkFrame(item_frame, fg_color="transparent")
        action_frame.grid(row=0, column=2, sticky="e", padx=15, pady=10)
        
        unprotect_btn = ctk.CTkButton(
            action_frame,
            text="🔓",
            width=40,
            command=lambda: self._unprotect_folder(folder_data),
            **ModernStyles.get_button_style('warning')
        )
        unprotect_btn.grid(row=0, column=0, padx=2)
        
        settings_btn = ctk.CTkButton(
            action_frame,
            text="⚙️",
            width=40,
            command=lambda: self._folder_settings(folder_data),
            **ModernStyles.get_button_style('secondary')
        )
        settings_btn.grid(row=0, column=1, padx=2)
    
    def update_status(self, message: str, status_type: str = "info"):
        """Update status bar message"""
        color_map = {
            'info': DARK_THEME['info_color'],
            'success': DARK_THEME['success_color'],
            'warning': DARK_THEME['warning_color'],
            'error': DARK_THEME['error_color']
        }
        
        self.status_label.configure(
            text=message,
            text_color=color_map.get(status_type, DARK_THEME['text_color'])
        )
        
        # Update indicator
        indicator_map = {
            'info': '🔵',
            'success': '🟢',
            'warning': '🟡',
            'error': '🔴'
        }
        
        self.status_indicator.configure(text=indicator_map.get(status_type, '🔵'))
    
    # Navigation methods
    def _show_folder_management(self):
        """Show folder management view"""
        self.page_title.configure(text="إدارة المجلدات")
        self._create_folder_management_view()
    
    def _show_protected_folders(self):
        """Show protected folders view"""
        self.page_title.configure(text="المجلدات المحمية")
        # Implementation for protected folders view
    
    def _show_activity_log(self):
        """Show activity log view"""
        self.page_title.configure(text="سجل الأنشطة")
        # Implementation for activity log view
    
    def _show_settings(self):
        """Show settings view"""
        self.page_title.configure(text="الإعدادات")
        # Implementation for settings view
    
    def _show_key_management(self):
        """Show key management view"""
        self.page_title.configure(text="إدارة المفاتيح")
        # Implementation for key management view
    
    # Quick action methods
    def _quick_protect_folder(self):
        """Quick protect folder action"""
        self._select_folder()
    
    def _quick_unprotect_folder(self):
        """Quick unprotect folder action"""
        # Implementation for quick unprotect
        pass
    
    def _unprotect_folder(self, folder_data: dict):
        """Unprotect a specific folder"""
        # Implementation for unprotecting folder
        pass
    
    def _folder_settings(self, folder_data: dict):
        """Show folder settings"""
        # Implementation for folder settings
        pass
    
    def _refresh_content(self):
        """Refresh current view content"""
        self._refresh_protected_folders()
        self.update_status("تم تحديث المحتوى", "success")
    
    def _on_search(self, event):
        """Handle search input"""
        search_term = self.search_entry.get().lower()
        # Implementation for search functionality
    
    def _logout(self):
        """Logout and return to login screen"""
        if messagebox.askyesno("تسجيل الخروج", "هل تريد تسجيل الخروج؟"):
            self.current_user = None
            self._show_login()
