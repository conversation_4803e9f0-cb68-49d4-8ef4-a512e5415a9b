#!/usr/bin/env python3
"""
FolderGuard - Advanced Folder Protection System
Main application entry point

Features:
- AES-256 encryption with PBKDF2 key derivation
- Two-factor authentication (TOTP)
- USB key authentication
- Modern GUI with dark theme
- Background service monitoring
- Encrypted logging with digital signatures
- Brute-force protection
- Drag and drop support

Author: AI Assistant
Version: 1.0.0
"""

import sys
import os
import argparse
import threading
import signal
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    import customtkinter as ctk
    from gui.main_window import MainWindow
    from core.service import FolderGuardService
    from core.security import SecurityManager
    from core.folder_manager import FolderManager
    from core.logger import SecureLogger
    from utils.config import ConfigManager
    from utils.database import SecureDatabase
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Please install required dependencies:")
    print("pip install -r requirements.txt")
    sys.exit(1)


class FolderGuardApp:
    """Main application class"""
    
    def __init__(self):
        self.config_manager = None
        self.database = None
        self.service = None
        self.gui = None
        self.running = False
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        print(f"\nReceived signal {signum}, shutting down...")
        self.shutdown()
        sys.exit(0)
    
    def initialize(self):
        """Initialize application components"""
        try:
            print("🔒 Initializing FolderGuard...")
            
            # Initialize configuration
            self.config_manager = ConfigManager()
            print("✅ Configuration loaded")
            
            # Initialize database
            db_path = self.config_manager.get_data_directory() / "folderguard.db"
            self.database = SecureDatabase(str(db_path))
            print("✅ Database initialized")
            
            # Create default admin user if none exists
            self._create_default_user()
            
            # Initialize service
            self.service = FolderGuardService(self.config_manager, self.database)
            print("✅ Service initialized")
            
            return True
            
        except Exception as e:
            print(f"❌ Initialization failed: {e}")
            return False
    
    def _create_default_user(self):
        """Create default admin user if database is empty"""
        try:
            # Check if any users exist
            existing_user = self.database.get_user(username="admin")
            
            if not existing_user:
                # Create default admin user
                security_manager = SecurityManager()
                password_hash = security_manager.hash_password("admin123")
                
                user_id = self.database.create_user(
                    username="admin",
                    email="<EMAIL>",
                    password_hash=password_hash
                )
                
                if user_id:
                    print("✅ Default admin user created (username: admin, password: admin123)")
                    print("⚠️  Please change the default password after first login!")
                else:
                    print("❌ Failed to create default user")
        
        except Exception as e:
            print(f"❌ Error creating default user: {e}")
    
    def run_gui(self):
        """Run the GUI application"""
        try:
            print("🖥️  Starting GUI...")
            
            # Configure CustomTkinter
            ctk.set_appearance_mode("dark")
            ctk.set_default_color_theme("blue")
            
            # Create and run main window
            self.gui = MainWindow()
            self.running = True
            
            print("✅ GUI started successfully")
            print("📁 FolderGuard is ready!")
            
            # Start the GUI main loop
            self.gui.mainloop()
            
        except Exception as e:
            print(f"❌ GUI error: {e}")
            return False
        finally:
            self.running = False
    
    def run_service(self):
        """Run as background service"""
        try:
            print("🔧 Starting background service...")
            
            self.service.start()
            self.running = True
            
            print("✅ Service started successfully")
            print("🛡️  FolderGuard service is monitoring...")
            
            # Keep service running
            try:
                while self.running:
                    import time
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n🛑 Service interrupted by user")
            
        except Exception as e:
            print(f"❌ Service error: {e}")
            return False
        finally:
            self.running = False
    
    def run_cli(self, args):
        """Run CLI commands"""
        try:
            if args.command == "status":
                self._show_status()
            elif args.command == "protect":
                self._protect_folder_cli(args.folder, args.password)
            elif args.command == "unprotect":
                self._unprotect_folder_cli(args.folder, args.password)
            elif args.command == "list":
                self._list_protected_folders()
            elif args.command == "logs":
                self._show_logs(args.lines)
            else:
                print(f"❌ Unknown command: {args.command}")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ CLI error: {e}")
            return False
    
    def _show_status(self):
        """Show application status"""
        print("\n📊 FolderGuard Status:")
        print("=" * 40)
        
        if self.service:
            status = self.service.get_status()
            print(f"Service Running: {'✅ Yes' if status['running'] else '❌ No'}")
            print(f"Protected Folders: {status['protected_folders']}")
            print(f"Active Sessions: {status['active_sessions']}")
            print(f"Monitoring Threads: {status['threads']}")
            print(f"USB Monitoring: {'✅ Enabled' if status['usb_monitoring'] else '❌ Disabled'}")
            print(f"Auto-lock: {'✅ Enabled' if status['auto_lock_enabled'] else '❌ Disabled'}")
        else:
            print("❌ Service not initialized")
    
    def _protect_folder_cli(self, folder_path: str, password: str):
        """Protect folder via CLI"""
        if not folder_path or not password:
            print("❌ Folder path and password are required")
            return
        
        if not os.path.exists(folder_path):
            print(f"❌ Folder not found: {folder_path}")
            return
        
        try:
            security_manager = SecurityManager()
            folder_manager = FolderManager(security_manager)
            
            success = folder_manager.protect_folder(folder_path, password, hide=True)
            
            if success:
                print(f"✅ Folder protected successfully: {folder_path}")
            else:
                print(f"❌ Failed to protect folder: {folder_path}")
                
        except Exception as e:
            print(f"❌ Error protecting folder: {e}")
    
    def _unprotect_folder_cli(self, folder_path: str, password: str):
        """Unprotect folder via CLI"""
        print("🔓 Unprotect functionality not implemented in CLI yet")
    
    def _list_protected_folders(self):
        """List all protected folders"""
        try:
            # Get protected folders from database
            # This is a simplified implementation
            print("\n📁 Protected Folders:")
            print("=" * 50)
            print("No protected folders found (CLI listing not fully implemented)")
            
        except Exception as e:
            print(f"❌ Error listing folders: {e}")
    
    def _show_logs(self, lines: int = 50):
        """Show recent log entries"""
        try:
            print(f"\n📋 Recent Log Entries (last {lines}):")
            print("=" * 60)
            print("Log viewing not fully implemented in CLI")
            
        except Exception as e:
            print(f"❌ Error showing logs: {e}")
    
    def shutdown(self):
        """Shutdown application"""
        print("\n🛑 Shutting down FolderGuard...")
        
        self.running = False
        
        # Stop service
        if self.service:
            self.service.stop()
            print("✅ Service stopped")
        
        # Close database
        if self.database:
            self.database.close()
            print("✅ Database closed")
        
        # Destroy GUI
        if self.gui:
            try:
                self.gui.quit()
                self.gui.destroy()
            except:
                pass
            print("✅ GUI closed")
        
        print("👋 FolderGuard shutdown complete")


def create_parser():
    """Create command line argument parser"""
    parser = argparse.ArgumentParser(
        description="FolderGuard - Advanced Folder Protection System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                          # Run GUI (default)
  python main.py --service                # Run as background service
  python main.py --cli status             # Show status
  python main.py --cli protect /path/to/folder --password mypass
  python main.py --cli list               # List protected folders
  python main.py --cli logs --lines 100   # Show last 100 log entries
        """
    )
    
    parser.add_argument(
        '--service',
        action='store_true',
        help='Run as background service'
    )
    
    parser.add_argument(
        '--cli',
        dest='command',
        choices=['status', 'protect', 'unprotect', 'list', 'logs'],
        help='Run CLI command'
    )
    
    parser.add_argument(
        '--folder',
        help='Folder path for protect/unprotect commands'
    )
    
    parser.add_argument(
        '--password',
        help='Password for protect/unprotect commands'
    )
    
    parser.add_argument(
        '--lines',
        type=int,
        default=50,
        help='Number of log lines to show (default: 50)'
    )
    
    parser.add_argument(
        '--version',
        action='version',
        version='FolderGuard 1.0.0'
    )
    
    return parser


def main():
    """Main entry point"""
    # Print banner
    print("=" * 60)
    print("🔒 FolderGuard - Advanced Folder Protection System")
    print("   Version 1.0.0")
    print("   Secure • Modern • Reliable")
    print("=" * 60)
    
    # Parse command line arguments
    parser = create_parser()
    args = parser.parse_args()
    
    # Create and initialize application
    app = FolderGuardApp()
    
    if not app.initialize():
        print("❌ Failed to initialize application")
        sys.exit(1)
    
    try:
        # Run based on arguments
        if args.service:
            # Run as service
            success = app.run_service()
        elif args.command:
            # Run CLI command
            success = app.run_cli(args)
        else:
            # Run GUI (default)
            success = app.run_gui()
        
        if not success:
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)
    finally:
        app.shutdown()


if __name__ == "__main__":
    main()
