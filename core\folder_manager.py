"""
Folder Management Module
Handles folder encryption, hiding, and protection
"""

import os
import shutil
import subprocess
import json
import time
from pathlib import Path
from typing import List, Dict, Optional
import zipfile
import tempfile
from .security import SecurityManager


class FolderManager:
    """Advanced folder management with encryption and hiding capabilities"""
    
    def __init__(self, security_manager: SecurityManager):
        self.security_manager = security_manager
        self.protected_folders = {}
        self.hidden_folders = set()
        self.temp_dir = tempfile.mkdtemp(prefix="folderguard_")
    
    def protect_folder(self, folder_path: str, password: str, hide: bool = True) -> bool:
        """
        Protect a folder with encryption and optional hiding
        """
        try:
            folder_path = os.path.abspath(folder_path)
            
            if not os.path.exists(folder_path):
                raise FileNotFoundError(f"Folder not found: {folder_path}")
            
            # Create encrypted archive
            archive_path = self._create_encrypted_archive(folder_path, password)
            
            if archive_path:
                # Store protection info
                folder_id = self.security_manager.generate_secure_token(16)
                self.protected_folders[folder_id] = {
                    'original_path': folder_path,
                    'archive_path': archive_path,
                    'protected_at': time.time(),
                    'hidden': hide
                }
                
                # Hide original folder if requested
                if hide:
                    self._hide_folder(folder_path)
                    self.hidden_folders.add(folder_path)
                
                return True
            
            return False
            
        except Exception as e:
            print(f"Error protecting folder: {e}")
            return False
    
    def unprotect_folder(self, folder_id: str, password: str) -> bool:
        """
        Unprotect and restore a folder
        """
        try:
            if folder_id not in self.protected_folders:
                return False
            
            folder_info = self.protected_folders[folder_id]
            archive_path = folder_info['archive_path']
            original_path = folder_info['original_path']
            
            # Decrypt and extract archive
            if self._extract_encrypted_archive(archive_path, password, original_path):
                # Unhide folder if it was hidden
                if folder_info['hidden']:
                    self._unhide_folder(original_path)
                    self.hidden_folders.discard(original_path)
                
                # Clean up
                os.remove(archive_path)
                del self.protected_folders[folder_id]
                
                return True
            
            return False
            
        except Exception as e:
            print(f"Error unprotecting folder: {e}")
            return False
    
    def _create_encrypted_archive(self, folder_path: str, password: str) -> Optional[str]:
        """Create encrypted ZIP archive of folder"""
        try:
            # Create temporary ZIP file
            archive_name = f"{os.path.basename(folder_path)}_{int(time.time())}.zip"
            temp_zip_path = os.path.join(self.temp_dir, archive_name)
            
            # Create ZIP archive
            with zipfile.ZipFile(temp_zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(folder_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arc_name = os.path.relpath(file_path, folder_path)
                        zipf.write(file_path, arc_name)
            
            # Encrypt the ZIP file
            with open(temp_zip_path, 'rb') as f:
                zip_data = f.read()
            
            encrypted_data, salt, iv = self.security_manager.encrypt_data(zip_data, password)
            
            # Save encrypted archive
            encrypted_archive_path = temp_zip_path + '.enc'
            with open(encrypted_archive_path, 'wb') as f:
                f.write(salt)
                f.write(iv)
                f.write(encrypted_data)
            
            # Clean up temporary ZIP
            os.remove(temp_zip_path)
            
            return encrypted_archive_path
            
        except Exception as e:
            print(f"Error creating encrypted archive: {e}")
            return None
    
    def _extract_encrypted_archive(self, archive_path: str, password: str, extract_path: str) -> bool:
        """Extract encrypted archive to specified path"""
        try:
            # Read encrypted archive
            with open(archive_path, 'rb') as f:
                salt = f.read(32)  # salt length
                iv = f.read(16)    # iv length
                encrypted_data = f.read()
            
            # Decrypt archive
            zip_data = self.security_manager.decrypt_data(encrypted_data, password, salt, iv)
            
            # Create temporary ZIP file
            temp_zip_path = os.path.join(self.temp_dir, f"temp_{int(time.time())}.zip")
            with open(temp_zip_path, 'wb') as f:
                f.write(zip_data)
            
            # Extract ZIP file
            with zipfile.ZipFile(temp_zip_path, 'r') as zipf:
                zipf.extractall(extract_path)
            
            # Clean up temporary ZIP
            os.remove(temp_zip_path)
            
            return True
            
        except Exception as e:
            print(f"Error extracting encrypted archive: {e}")
            return False
    
    def _hide_folder(self, folder_path: str):
        """Hide folder using system attributes"""
        try:
            if os.name == 'nt':  # Windows
                # Set hidden attribute
                subprocess.run(['attrib', '+H', folder_path], check=True)
                # Also rename with dot prefix for extra hiding
                hidden_path = os.path.join(os.path.dirname(folder_path), 
                                         f".{os.path.basename(folder_path)}_hidden")
                if not os.path.exists(hidden_path):
                    os.rename(folder_path, hidden_path)
            else:  # Unix-like systems
                # Rename with dot prefix
                hidden_path = os.path.join(os.path.dirname(folder_path), 
                                         f".{os.path.basename(folder_path)}")
                if not os.path.exists(hidden_path):
                    os.rename(folder_path, hidden_path)
        except Exception as e:
            print(f"Error hiding folder: {e}")
    
    def _unhide_folder(self, folder_path: str):
        """Unhide folder"""
        try:
            if os.name == 'nt':  # Windows
                # Remove hidden attribute
                subprocess.run(['attrib', '-H', folder_path], check=True)
                # Check for renamed hidden folder
                hidden_path = os.path.join(os.path.dirname(folder_path), 
                                         f".{os.path.basename(folder_path)}_hidden")
                if os.path.exists(hidden_path):
                    os.rename(hidden_path, folder_path)
            else:  # Unix-like systems
                # Check for dot-prefixed folder
                hidden_path = os.path.join(os.path.dirname(folder_path), 
                                         f".{os.path.basename(folder_path)}")
                if os.path.exists(hidden_path):
                    os.rename(hidden_path, folder_path)
        except Exception as e:
            print(f"Error unhiding folder: {e}")
    
    def get_protected_folders(self) -> Dict:
        """Get list of protected folders"""
        return self.protected_folders.copy()
    
    def is_folder_protected(self, folder_path: str) -> bool:
        """Check if folder is protected"""
        folder_path = os.path.abspath(folder_path)
        for folder_info in self.protected_folders.values():
            if folder_info['original_path'] == folder_path:
                return True
        return False
    
    def cleanup_temp_files(self):
        """Clean up temporary files"""
        try:
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                self.temp_dir = tempfile.mkdtemp(prefix="folderguard_")
        except Exception as e:
            print(f"Error cleaning up temp files: {e}")
    
    def save_protection_state(self, file_path: str, master_password: str):
        """Save protection state to encrypted file"""
        try:
            state_data = {
                'protected_folders': self.protected_folders,
                'hidden_folders': list(self.hidden_folders),
                'saved_at': time.time()
            }
            
            json_data = json.dumps(state_data).encode('utf-8')
            encrypted_data, salt, iv = self.security_manager.encrypt_data(json_data, master_password)
            
            with open(file_path, 'wb') as f:
                f.write(salt)
                f.write(iv)
                f.write(encrypted_data)
            
            return True
        except Exception as e:
            print(f"Error saving protection state: {e}")
            return False
    
    def load_protection_state(self, file_path: str, master_password: str):
        """Load protection state from encrypted file"""
        try:
            if not os.path.exists(file_path):
                return False
            
            with open(file_path, 'rb') as f:
                salt = f.read(32)
                iv = f.read(16)
                encrypted_data = f.read()
            
            json_data = self.security_manager.decrypt_data(encrypted_data, master_password, salt, iv)
            state_data = json.loads(json_data.decode('utf-8'))
            
            self.protected_folders = state_data.get('protected_folders', {})
            self.hidden_folders = set(state_data.get('hidden_folders', []))
            
            return True
        except Exception as e:
            print(f"Error loading protection state: {e}")
            return False
