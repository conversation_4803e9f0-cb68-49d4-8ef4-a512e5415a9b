"""
Encrypted Logging System
Secure logging with digital signatures and encryption
"""

import os
import json
import time
import hashlib
import hmac
from datetime import datetime
from typing import Dict, List, Optional
from .security import SecurityManager


class SecureLogger:
    """Encrypted logger with digital signatures"""
    
    def __init__(self, security_manager: SecurityManager, log_file: str = "security.log"):
        self.security_manager = security_manager
        self.log_file = log_file
        self.log_key = None
        self.signature_key = None
        self._initialize_keys()
    
    def _initialize_keys(self):
        """Initialize encryption and signature keys"""
        # Generate or load keys for log encryption
        key_file = self.log_file + ".key"
        if os.path.exists(key_file):
            try:
                with open(key_file, 'rb') as f:
                    self.log_key = f.read(32)
                    self.signature_key = f.read(32)
            except Exception:
                self._generate_new_keys()
        else:
            self._generate_new_keys()
    
    def _generate_new_keys(self):
        """Generate new encryption and signature keys"""
        import secrets
        self.log_key = secrets.token_bytes(32)
        self.signature_key = secrets.token_bytes(32)
        
        key_file = self.log_file + ".key"
        with open(key_file, 'wb') as f:
            f.write(self.log_key)
            f.write(self.signature_key)
        
        # Set file permissions (read-only for owner)
        if os.name != 'nt':
            os.chmod(key_file, 0o600)
    
    def _create_signature(self, data: str) -> str:
        """Create HMAC signature for log entry"""
        return hmac.new(
            self.signature_key,
            data.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def _verify_signature(self, data: str, signature: str) -> bool:
        """Verify HMAC signature"""
        expected_signature = self._create_signature(data)
        return hmac.compare_digest(expected_signature, signature)
    
    def log_event(self, event_type: str, message: str, user_id: str = None, 
                  ip_address: str = None, additional_data: Dict = None):
        """Log security event with encryption and signature"""
        try:
            timestamp = datetime.now().isoformat()
            
            log_entry = {
                'timestamp': timestamp,
                'event_type': event_type,
                'message': message,
                'user_id': user_id,
                'ip_address': ip_address,
                'additional_data': additional_data or {},
                'log_id': self.security_manager.generate_secure_token(16)
            }
            
            # Convert to JSON
            json_data = json.dumps(log_entry, sort_keys=True)
            
            # Create signature
            signature = self._create_signature(json_data)
            
            # Create final log entry with signature
            signed_entry = {
                'data': json_data,
                'signature': signature
            }
            
            # Encrypt the signed entry
            entry_bytes = json.dumps(signed_entry).encode('utf-8')
            salt = self.security_manager.generate_salt()
            iv = self.security_manager.generate_iv()
            
            # Use log key for encryption
            from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
            from cryptography.hazmat.backends import default_backend
            
            cipher = Cipher(
                algorithms.AES(self.log_key),
                modes.CBC(iv),
                backend=default_backend()
            )
            encryptor = cipher.encryptor()
            
            # Add padding
            padded_data = self.security_manager._add_padding(entry_bytes)
            encrypted_data = encryptor.update(padded_data) + encryptor.finalize()
            
            # Write to log file
            with open(self.log_file, 'ab') as f:
                # Write entry length, salt, iv, and encrypted data
                entry_length = len(encrypted_data)
                f.write(entry_length.to_bytes(4, 'big'))
                f.write(salt)
                f.write(iv)
                f.write(encrypted_data)
            
        except Exception as e:
            # Fallback to plain text logging if encryption fails
            with open(self.log_file + ".error", 'a') as f:
                f.write(f"{datetime.now().isoformat()} - Logging error: {e}\n")
    
    def read_logs(self, start_time: Optional[datetime] = None, 
                  end_time: Optional[datetime] = None,
                  event_type: Optional[str] = None) -> List[Dict]:
        """Read and decrypt log entries"""
        try:
            if not os.path.exists(self.log_file):
                return []
            
            logs = []
            
            with open(self.log_file, 'rb') as f:
                while True:
                    # Read entry length
                    length_bytes = f.read(4)
                    if len(length_bytes) < 4:
                        break
                    
                    entry_length = int.from_bytes(length_bytes, 'big')
                    
                    # Read salt, iv, and encrypted data
                    salt = f.read(32)
                    iv = f.read(16)
                    encrypted_data = f.read(entry_length)
                    
                    if len(encrypted_data) < entry_length:
                        break
                    
                    try:
                        # Decrypt entry
                        from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
                        from cryptography.hazmat.backends import default_backend
                        
                        cipher = Cipher(
                            algorithms.AES(self.log_key),
                            modes.CBC(iv),
                            backend=default_backend()
                        )
                        decryptor = cipher.decryptor()
                        
                        padded_data = decryptor.update(encrypted_data) + decryptor.finalize()
                        entry_bytes = self.security_manager._remove_padding(padded_data)
                        
                        # Parse signed entry
                        signed_entry = json.loads(entry_bytes.decode('utf-8'))
                        data = signed_entry['data']
                        signature = signed_entry['signature']
                        
                        # Verify signature
                        if self._verify_signature(data, signature):
                            log_entry = json.loads(data)
                            
                            # Apply filters
                            entry_time = datetime.fromisoformat(log_entry['timestamp'])
                            
                            if start_time and entry_time < start_time:
                                continue
                            if end_time and entry_time > end_time:
                                continue
                            if event_type and log_entry['event_type'] != event_type:
                                continue
                            
                            logs.append(log_entry)
                        
                    except Exception as e:
                        # Skip corrupted entries
                        continue
            
            return logs
            
        except Exception as e:
            print(f"Error reading logs: {e}")
            return []
    
    def log_login_attempt(self, success: bool, user_id: str = None, ip_address: str = None):
        """Log login attempt"""
        event_type = "LOGIN_SUCCESS" if success else "LOGIN_FAILED"
        message = f"Login {'successful' if success else 'failed'}"
        self.log_event(event_type, message, user_id, ip_address)
    
    def log_folder_operation(self, operation: str, folder_path: str, user_id: str = None):
        """Log folder protection/unprotection operations"""
        self.log_event(
            "FOLDER_OPERATION",
            f"Folder {operation}: {folder_path}",
            user_id,
            additional_data={'operation': operation, 'folder_path': folder_path}
        )
    
    def log_security_event(self, event: str, details: str, user_id: str = None):
        """Log general security events"""
        self.log_event("SECURITY_EVENT", f"{event}: {details}", user_id)
    
    def log_system_event(self, event: str, details: str):
        """Log system events"""
        self.log_event("SYSTEM_EVENT", f"{event}: {details}")
    
    def get_failed_login_attempts(self, time_window: int = 3600) -> List[Dict]:
        """Get failed login attempts within time window (seconds)"""
        end_time = datetime.now()
        start_time = datetime.fromtimestamp(end_time.timestamp() - time_window)
        
        logs = self.read_logs(start_time, end_time, "LOGIN_FAILED")
        return logs
    
    def export_logs(self, output_file: str, password: str, 
                   start_time: Optional[datetime] = None,
                   end_time: Optional[datetime] = None) -> bool:
        """Export logs to encrypted file"""
        try:
            logs = self.read_logs(start_time, end_time)
            
            export_data = {
                'exported_at': datetime.now().isoformat(),
                'log_count': len(logs),
                'logs': logs
            }
            
            json_data = json.dumps(export_data, indent=2).encode('utf-8')
            encrypted_data, salt, iv = self.security_manager.encrypt_data(json_data, password)
            
            with open(output_file, 'wb') as f:
                f.write(salt)
                f.write(iv)
                f.write(encrypted_data)
            
            return True
            
        except Exception as e:
            print(f"Error exporting logs: {e}")
            return False
