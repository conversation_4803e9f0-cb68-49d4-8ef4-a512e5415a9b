"""
Modern Dark Theme Styles for CustomTkinter
"""

import customtkinter as ctk
from typing import Dict, Any

# Color scheme for dark theme
DARK_THEME = {
    'bg_color': '#1a1a1a',
    'fg_color': '#2b2b2b', 
    'hover_color': '#3d3d3d',
    'selected_color': '#4a9eff',
    'text_color': '#ffffff',
    'text_color_disabled': '#6b6b6b',
    'button_color': '#4a9eff',
    'button_hover_color': '#5ba7ff',
    'entry_color': '#2b2b2b',
    'entry_border_color': '#4a4a4a',
    'success_color': '#4caf50',
    'warning_color': '#ff9800',
    'error_color': '#f44336',
    'info_color': '#2196f3'
}

# Font configurations
FONTS = {
    'title': ('Segoe UI', 24, 'bold'),
    'heading': ('Segoe UI', 18, 'bold'),
    'subheading': ('Segoe UI', 14, 'bold'),
    'body': ('Segoe UI', 12),
    'small': ('Segoe UI', 10),
    'code': ('Consolas', 11)
}

class ModernStyles:
    """Modern styling for the application"""
    
    @staticmethod
    def configure_theme():
        """Configure the global theme"""
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
    
    @staticmethod
    def get_button_style(style_type: str = 'primary') -> Dict[str, Any]:
        """Get button styling"""
        base_style = {
            'font': FONTS['body'],
            'corner_radius': 8,
            'height': 40,
            'border_width': 0
        }
        
        if style_type == 'primary':
            base_style.update({
                'fg_color': DARK_THEME['button_color'],
                'hover_color': DARK_THEME['button_hover_color'],
                'text_color': 'white'
            })
        elif style_type == 'secondary':
            base_style.update({
                'fg_color': DARK_THEME['fg_color'],
                'hover_color': DARK_THEME['hover_color'],
                'text_color': DARK_THEME['text_color'],
                'border_width': 1,
                'border_color': DARK_THEME['entry_border_color']
            })
        elif style_type == 'success':
            base_style.update({
                'fg_color': DARK_THEME['success_color'],
                'hover_color': '#45a049',
                'text_color': 'white'
            })
        elif style_type == 'danger':
            base_style.update({
                'fg_color': DARK_THEME['error_color'],
                'hover_color': '#da190b',
                'text_color': 'white'
            })
        elif style_type == 'warning':
            base_style.update({
                'fg_color': DARK_THEME['warning_color'],
                'hover_color': '#e68900',
                'text_color': 'white'
            })
        
        return base_style
    
    @staticmethod
    def get_entry_style() -> Dict[str, Any]:
        """Get entry field styling"""
        return {
            'font': FONTS['body'],
            'corner_radius': 8,
            'height': 40,
            'border_width': 1,
            'border_color': DARK_THEME['entry_border_color'],
            'fg_color': DARK_THEME['entry_color'],
            'text_color': DARK_THEME['text_color']
        }
    
    @staticmethod
    def get_frame_style(style_type: str = 'default') -> Dict[str, Any]:
        """Get frame styling"""
        base_style = {
            'corner_radius': 12,
            'border_width': 0
        }
        
        if style_type == 'card':
            base_style.update({
                'fg_color': DARK_THEME['fg_color'],
                'border_width': 1,
                'border_color': DARK_THEME['entry_border_color']
            })
        elif style_type == 'sidebar':
            base_style.update({
                'fg_color': DARK_THEME['bg_color'],
                'corner_radius': 0
            })
        
        return base_style
    
    @staticmethod
    def get_label_style(style_type: str = 'default') -> Dict[str, Any]:
        """Get label styling"""
        base_style = {
            'text_color': DARK_THEME['text_color'],
            'font': FONTS['body']
        }
        
        if style_type == 'title':
            base_style.update({
                'font': FONTS['title'],
                'text_color': DARK_THEME['selected_color']
            })
        elif style_type == 'heading':
            base_style.update({
                'font': FONTS['heading']
            })
        elif style_type == 'subheading':
            base_style.update({
                'font': FONTS['subheading']
            })
        elif style_type == 'small':
            base_style.update({
                'font': FONTS['small'],
                'text_color': DARK_THEME['text_color_disabled']
            })
        elif style_type == 'success':
            base_style.update({
                'text_color': DARK_THEME['success_color']
            })
        elif style_type == 'error':
            base_style.update({
                'text_color': DARK_THEME['error_color']
            })
        elif style_type == 'warning':
            base_style.update({
                'text_color': DARK_THEME['warning_color']
            })
        
        return base_style
    
    @staticmethod
    def get_progressbar_style() -> Dict[str, Any]:
        """Get progress bar styling"""
        return {
            'corner_radius': 8,
            'height': 20,
            'progress_color': DARK_THEME['selected_color'],
            'fg_color': DARK_THEME['fg_color']
        }
    
    @staticmethod
    def get_switch_style() -> Dict[str, Any]:
        """Get switch styling"""
        return {
            'progress_color': DARK_THEME['selected_color'],
            'button_color': DARK_THEME['text_color'],
            'button_hover_color': DARK_THEME['hover_color']
        }
    
    @staticmethod
    def get_scrollbar_style() -> Dict[str, Any]:
        """Get scrollbar styling"""
        return {
            'corner_radius': 6,
            'border_spacing': 4,
            'fg_color': DARK_THEME['fg_color'],
            'button_color': DARK_THEME['hover_color'],
            'button_hover_color': DARK_THEME['selected_color']
        }


class IconManager:
    """Manage SVG icons for the application"""
    
    # SVG icon definitions
    ICONS = {
        'lock': '''<svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zM12 17c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zM15.1 8H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z"/>
        </svg>''',
        
        'unlock': '''<svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 17c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm6-9h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6h1.9c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2z"/>
        </svg>''',
        
        'folder': '''<svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z"/>
        </svg>''',
        
        'shield': '''<svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11.5C15.4,11.5 16,12.1 16,12.7V16.2C16,16.8 15.4,17.3 14.8,17.3H9.2C8.6,17.3 8,16.8 8,16.2V12.7C8,12.1 8.6,11.5 9.2,11.5V10C9.2,8.6 10.6,7 12,7M12,8.2C11.2,8.2 10.5,8.7 10.5,10V11.5H13.5V10C13.5,8.7 12.8,8.2 12,8.2Z"/>
        </svg>''',
        
        'settings': '''<svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
        </svg>''',
        
        'eye': '''<svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z"/>
        </svg>''',
        
        'eye_off': '''<svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M11.83,9L15,12.16C15,12.11 15,12.05 15,12A3,3 0 0,0 12,9C11.94,9 11.89,9 11.83,9M7.53,9.8L9.08,11.35C9.03,11.56 9,11.77 9,12A3,3 0 0,0 12,15C12.22,15 12.44,14.97 12.65,14.92L14.2,16.47C13.53,16.8 12.79,17 12,17A5,5 0 0,1 7,12C7,11.21 7.2,10.47 7.53,9.8M2,4.27L4.28,6.55L4.73,7C3.08,8.3 1.78,10 1,12C2.73,16.39 7,19.5 12,19.5C13.55,19.5 15.03,19.2 16.38,18.66L16.81,19.09L19.73,22L21,20.73L3.27,3M12,7A5,5 0 0,1 17,12C17,12.64 16.87,13.26 16.64,13.82L19.57,16.75C21.07,15.5 22.27,13.86 23,12C21.27,7.61 17,4.5 12,4.5C10.6,4.5 9.26,4.75 8,5.2L10.17,7.35C10.76,7.13 11.37,7 12,7Z"/>
        </svg>'''
    }
    
    @staticmethod
    def get_icon(name: str, size: int = 24, color: str = None) -> str:
        """Get SVG icon with specified size and color"""
        if name not in IconManager.ICONS:
            return ""
        
        icon = IconManager.ICONS[name]
        
        # Replace size
        icon = icon.replace('width="24"', f'width="{size}"')
        icon = icon.replace('height="24"', f'height="{size}"')
        
        # Replace color if specified
        if color:
            icon = icon.replace('fill="currentColor"', f'fill="{color}"')
        
        return icon
