"""
Folder Protection Dialog
Advanced protection settings and options
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
import threading
from typing import List, Dict, Optional
from .styles import ModernStyles, DARK_THEME, FONTS
import os


class ProtectionDialog(ctk.CTkToplevel):
    """Dialog for configuring folder protection"""
    
    def __init__(self, parent, folders: List[str]):
        super().__init__(parent)
        
        self.folders = folders
        self.result = None
        self.protection_settings = {
            'password': '',
            'hide_folders': True,
            'encryption_level': 'high',
            'backup_before_protect': True,
            'auto_lock_timeout': 30,
            'require_2fa': False,
            'require_usb_key': False
        }
        
        self._setup_window()
        self._create_widgets()
        self._setup_bindings()
        
        # Center and make modal
        self.center_window()
        self.transient(parent)
        self.grab_set()
        
        # Focus on password entry
        self.password_entry.focus()
    
    def _setup_window(self):
        """Setup window properties"""
        self.title("إعدادات حماية المجلدات")
        self.geometry("600x800")
        self.resizable(False, False)
        
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)
        
        ModernStyles.configure_theme()
    
    def _create_widgets(self):
        """Create and layout widgets"""
        # Main scrollable frame
        self.main_frame = ctk.CTkScrollableFrame(
            self,
            **ModernStyles.get_frame_style('card')
        )
        self.main_frame.grid(row=0, column=0, sticky="nsew", padx=20, pady=20)
        self.main_frame.grid_columnconfigure(0, weight=1)
        
        # Title
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="🛡️ إعدادات الحماية المتقدمة",
            **ModernStyles.get_label_style('title')
        )
        title_label.grid(row=0, column=0, pady=(20, 10))
        
        # Folder list
        self._create_folder_list()
        
        # Security settings
        self._create_security_settings()
        
        # Advanced options
        self._create_advanced_options()
        
        # Action buttons
        self._create_action_buttons()
    
    def _create_folder_list(self):
        """Create folder list section"""
        folder_frame = ctk.CTkFrame(self.main_frame, **ModernStyles.get_frame_style('card'))
        folder_frame.grid(row=1, column=0, sticky="ew", pady=(20, 15))
        folder_frame.grid_columnconfigure(0, weight=1)
        
        # Section title
        ctk.CTkLabel(
            folder_frame,
            text="المجلدات المحددة للحماية:",
            **ModernStyles.get_label_style('subheading')
        ).grid(row=0, column=0, sticky="w", padx=20, pady=(15, 10))
        
        # Folder list
        for i, folder in enumerate(self.folders):
            folder_item = ctk.CTkFrame(folder_frame, fg_color="transparent")
            folder_item.grid(row=i+1, column=0, sticky="ew", padx=20, pady=2)
            folder_item.grid_columnconfigure(1, weight=1)
            
            # Folder icon
            ctk.CTkLabel(
                folder_item,
                text="📁",
                font=FONTS['body']
            ).grid(row=0, column=0, padx=(0, 10))
            
            # Folder path
            ctk.CTkLabel(
                folder_item,
                text=os.path.basename(folder),
                **ModernStyles.get_label_style('body')
            ).grid(row=0, column=1, sticky="w")
            
            # Folder size (mock)
            ctk.CTkLabel(
                folder_item,
                text="125 MB",
                **ModernStyles.get_label_style('small')
            ).grid(row=0, column=2, padx=(10, 0))
        
        # Add padding at bottom
        ctk.CTkLabel(folder_frame, text="").grid(row=len(self.folders)+1, column=0, pady=10)
    
    def _create_security_settings(self):
        """Create security settings section"""
        security_frame = ctk.CTkFrame(self.main_frame, **ModernStyles.get_frame_style('card'))
        security_frame.grid(row=2, column=0, sticky="ew", pady=15)
        security_frame.grid_columnconfigure(0, weight=1)
        
        # Section title
        ctk.CTkLabel(
            security_frame,
            text="🔐 إعدادات الأمان",
            **ModernStyles.get_label_style('subheading')
        ).grid(row=0, column=0, sticky="w", padx=20, pady=(15, 20))
        
        # Password section
        password_frame = ctk.CTkFrame(security_frame, fg_color="transparent")
        password_frame.grid(row=1, column=0, sticky="ew", padx=20, pady=(0, 15))
        password_frame.grid_columnconfigure(0, weight=1)
        
        ctk.CTkLabel(
            password_frame,
            text="كلمة المرور الرئيسية:",
            **ModernStyles.get_label_style('body')
        ).grid(row=0, column=0, sticky="w", pady=(0, 5))
        
        self.password_entry = ctk.CTkEntry(
            password_frame,
            placeholder_text="أدخل كلمة مرور قوية",
            show="*",
            **ModernStyles.get_entry_style()
        )
        self.password_entry.grid(row=1, column=0, sticky="ew", pady=(0, 10))
        
        # Password confirmation
        ctk.CTkLabel(
            password_frame,
            text="تأكيد كلمة المرور:",
            **ModernStyles.get_label_style('body')
        ).grid(row=2, column=0, sticky="w", pady=(0, 5))
        
        self.confirm_password_entry = ctk.CTkEntry(
            password_frame,
            placeholder_text="أعد إدخال كلمة المرور",
            show="*",
            **ModernStyles.get_entry_style()
        )
        self.confirm_password_entry.grid(row=3, column=0, sticky="ew", pady=(0, 10))
        
        # Password strength indicator
        self.strength_frame = ctk.CTkFrame(password_frame, fg_color="transparent")
        self.strength_frame.grid(row=4, column=0, sticky="ew", pady=(0, 15))
        self.strength_frame.grid_columnconfigure(1, weight=1)
        
        ctk.CTkLabel(
            self.strength_frame,
            text="قوة كلمة المرور:",
            **ModernStyles.get_label_style('small')
        ).grid(row=0, column=0, sticky="w")
        
        self.strength_bar = ctk.CTkProgressBar(
            self.strength_frame,
            width=200,
            **ModernStyles.get_progressbar_style()
        )
        self.strength_bar.grid(row=0, column=1, sticky="ew", padx=(10, 0))
        self.strength_bar.set(0)
        
        self.strength_label = ctk.CTkLabel(
            self.strength_frame,
            text="ضعيفة",
            **ModernStyles.get_label_style('small')
        )
        self.strength_label.grid(row=0, column=2, padx=(10, 0))
        
        # Encryption level
        encryption_frame = ctk.CTkFrame(security_frame, fg_color="transparent")
        encryption_frame.grid(row=2, column=0, sticky="ew", padx=20, pady=(0, 20))
        encryption_frame.grid_columnconfigure(1, weight=1)
        
        ctk.CTkLabel(
            encryption_frame,
            text="مستوى التشفير:",
            **ModernStyles.get_label_style('body')
        ).grid(row=0, column=0, sticky="w", pady=(0, 10))
        
        self.encryption_var = ctk.StringVar(value="high")
        encryption_options = [
            ("عالي (AES-256)", "high"),
            ("متوسط (AES-192)", "medium"),
            ("أساسي (AES-128)", "basic")
        ]
        
        for i, (text, value) in enumerate(encryption_options):
            radio = ctk.CTkRadioButton(
                encryption_frame,
                text=text,
                variable=self.encryption_var,
                value=value,
                **ModernStyles.get_label_style('body')
            )
            radio.grid(row=i+1, column=0, sticky="w", pady=2)
    
    def _create_advanced_options(self):
        """Create advanced options section"""
        advanced_frame = ctk.CTkFrame(self.main_frame, **ModernStyles.get_frame_style('card'))
        advanced_frame.grid(row=3, column=0, sticky="ew", pady=15)
        advanced_frame.grid_columnconfigure(0, weight=1)
        
        # Section title
        ctk.CTkLabel(
            advanced_frame,
            text="⚙️ خيارات متقدمة",
            **ModernStyles.get_label_style('subheading')
        ).grid(row=0, column=0, sticky="w", padx=20, pady=(15, 20))
        
        # Options frame
        options_frame = ctk.CTkFrame(advanced_frame, fg_color="transparent")
        options_frame.grid(row=1, column=0, sticky="ew", padx=20, pady=(0, 20))
        options_frame.grid_columnconfigure(0, weight=1)
        
        # Hide folders option
        self.hide_var = ctk.BooleanVar(value=True)
        hide_switch = ctk.CTkSwitch(
            options_frame,
            text="إخفاء المجلدات بعد الحماية",
            variable=self.hide_var,
            **ModernStyles.get_switch_style()
        )
        hide_switch.grid(row=0, column=0, sticky="w", pady=5)
        
        # Backup option
        self.backup_var = ctk.BooleanVar(value=True)
        backup_switch = ctk.CTkSwitch(
            options_frame,
            text="إنشاء نسخة احتياطية قبل الحماية",
            variable=self.backup_var,
            **ModernStyles.get_switch_style()
        )
        backup_switch.grid(row=1, column=0, sticky="w", pady=5)
        
        # 2FA option
        self.twofa_var = ctk.BooleanVar(value=False)
        twofa_switch = ctk.CTkSwitch(
            options_frame,
            text="تطلب المصادقة الثنائية",
            variable=self.twofa_var,
            **ModernStyles.get_switch_style()
        )
        twofa_switch.grid(row=2, column=0, sticky="w", pady=5)
        
        # USB Key option
        self.usb_var = ctk.BooleanVar(value=False)
        usb_switch = ctk.CTkSwitch(
            options_frame,
            text="تطلب مفتاح USB للوصول",
            variable=self.usb_var,
            **ModernStyles.get_switch_style()
        )
        usb_switch.grid(row=3, column=0, sticky="w", pady=5)
        
        # Auto-lock timeout
        timeout_frame = ctk.CTkFrame(options_frame, fg_color="transparent")
        timeout_frame.grid(row=4, column=0, sticky="ew", pady=(15, 5))
        timeout_frame.grid_columnconfigure(1, weight=1)
        
        ctk.CTkLabel(
            timeout_frame,
            text="مهلة القفل التلقائي (دقائق):",
            **ModernStyles.get_label_style('body')
        ).grid(row=0, column=0, sticky="w")
        
        self.timeout_var = ctk.StringVar(value="30")
        timeout_entry = ctk.CTkEntry(
            timeout_frame,
            textvariable=self.timeout_var,
            width=100,
            **ModernStyles.get_entry_style()
        )
        timeout_entry.grid(row=0, column=1, sticky="e", padx=(10, 0))
    
    def _create_action_buttons(self):
        """Create action buttons"""
        button_frame = ctk.CTkFrame(self.main_frame, fg_color="transparent")
        button_frame.grid(row=4, column=0, sticky="ew", pady=(20, 30))
        button_frame.grid_columnconfigure((0, 1), weight=1)
        
        # Cancel button
        cancel_btn = ctk.CTkButton(
            button_frame,
            text="إلغاء",
            command=self.destroy,
            **ModernStyles.get_button_style('secondary')
        )
        cancel_btn.grid(row=0, column=0, sticky="ew", padx=(0, 10))
        
        # Protect button
        self.protect_btn = ctk.CTkButton(
            button_frame,
            text="🛡️ تطبيق الحماية",
            command=self._apply_protection,
            **ModernStyles.get_button_style('primary')
        )
        self.protect_btn.grid(row=0, column=1, sticky="ew", padx=(10, 0))
        
        # Progress bar (hidden initially)
        self.progress_bar = ctk.CTkProgressBar(
            self.main_frame,
            **ModernStyles.get_progressbar_style()
        )
        self.progress_bar.grid(row=5, column=0, sticky="ew", padx=20, pady=(0, 20))
        self.progress_bar.grid_remove()
        
        # Status label
        self.status_label = ctk.CTkLabel(
            self.main_frame,
            text="",
            **ModernStyles.get_label_style('small')
        )
        self.status_label.grid(row=6, column=0, pady=(0, 20))
    
    def _setup_bindings(self):
        """Setup event bindings"""
        # Password strength checking
        self.password_entry.bind('<KeyRelease>', self._check_password_strength)
        self.confirm_password_entry.bind('<KeyRelease>', self._check_password_match)
        
        # Enter key binding
        self.bind('<Return>', lambda e: self._apply_protection())
        self.bind('<Escape>', lambda e: self.destroy())
    
    def center_window(self):
        """Center the window on screen"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f"{width}x{height}+{x}+{y}")
    
    def _check_password_strength(self, event=None):
        """Check and display password strength"""
        password = self.password_entry.get()
        
        if not password:
            self.strength_bar.set(0)
            self.strength_label.configure(text="", text_color=DARK_THEME['text_color'])
            return
        
        # Calculate strength
        strength = 0
        
        # Length check
        if len(password) >= 8:
            strength += 1
        if len(password) >= 12:
            strength += 1
        
        # Character variety
        if any(c.islower() for c in password):
            strength += 1
        if any(c.isupper() for c in password):
            strength += 1
        if any(c.isdigit() for c in password):
            strength += 1
        if any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            strength += 1
        
        # Update display
        strength_ratio = min(strength / 6, 1.0)
        self.strength_bar.set(strength_ratio)
        
        if strength <= 2:
            self.strength_label.configure(text="ضعيفة", text_color=DARK_THEME['error_color'])
        elif strength <= 4:
            self.strength_label.configure(text="متوسطة", text_color=DARK_THEME['warning_color'])
        else:
            self.strength_label.configure(text="قوية", text_color=DARK_THEME['success_color'])
    
    def _check_password_match(self, event=None):
        """Check if passwords match"""
        password = self.password_entry.get()
        confirm = self.confirm_password_entry.get()
        
        if confirm and password != confirm:
            self.confirm_password_entry.configure(border_color=DARK_THEME['error_color'])
        else:
            self.confirm_password_entry.configure(border_color=DARK_THEME['entry_border_color'])
    
    def _apply_protection(self):
        """Apply protection to folders"""
        # Validate inputs
        if not self._validate_inputs():
            return
        
        # Collect settings
        self.protection_settings.update({
            'password': self.password_entry.get(),
            'hide_folders': self.hide_var.get(),
            'encryption_level': self.encryption_var.get(),
            'backup_before_protect': self.backup_var.get(),
            'auto_lock_timeout': int(self.timeout_var.get()),
            'require_2fa': self.twofa_var.get(),
            'require_usb_key': self.usb_var.get()
        })
        
        # Disable button and show progress
        self.protect_btn.configure(state="disabled")
        self.progress_bar.grid()
        self.progress_bar.start()
        
        # Start protection process
        threading.Thread(target=self._protection_worker, daemon=True).start()
    
    def _validate_inputs(self) -> bool:
        """Validate user inputs"""
        password = self.password_entry.get()
        confirm = self.confirm_password_entry.get()
        
        if not password:
            self._show_error("يرجى إدخال كلمة مرور")
            return False
        
        if len(password) < 8:
            self._show_error("كلمة المرور يجب أن تكون 8 أحرف على الأقل")
            return False
        
        if password != confirm:
            self._show_error("كلمات المرور غير متطابقة")
            return False
        
        try:
            timeout = int(self.timeout_var.get())
            if timeout < 1 or timeout > 1440:  # 1 minute to 24 hours
                self._show_error("مهلة القفل يجب أن تكون بين 1 و 1440 دقيقة")
                return False
        except ValueError:
            self._show_error("مهلة القفل يجب أن تكون رقماً صحيحاً")
            return False
        
        return True
    
    def _show_error(self, message: str):
        """Show error message"""
        self.status_label.configure(text=message, text_color=DARK_THEME['error_color'])
    
    def _protection_worker(self):
        """Background worker for protection process"""
        try:
            # Simulate protection process
            import time
            
            for i, folder in enumerate(self.folders):
                progress = (i + 1) / len(self.folders)
                self.after(0, lambda p=progress: self.progress_bar.set(p))
                self.after(0, lambda f=folder: self.status_label.configure(
                    text=f"جاري حماية: {os.path.basename(f)}",
                    text_color=DARK_THEME['info_color']
                ))
                
                # Simulate work
                time.sleep(2)
            
            # Success
            self.after(0, self._protection_complete)
            
        except Exception as e:
            self.after(0, lambda: self._protection_error(str(e)))
    
    def _protection_complete(self):
        """Handle successful protection completion"""
        self.progress_bar.stop()
        self.progress_bar.set(1.0)
        self.status_label.configure(
            text="تم تطبيق الحماية بنجاح!",
            text_color=DARK_THEME['success_color']
        )
        
        self.result = {
            'success': True,
            'folders': self.folders,
            'settings': self.protection_settings
        }
        
        # Close dialog after delay
        self.after(2000, self.destroy)
    
    def _protection_error(self, error_message: str):
        """Handle protection error"""
        self.progress_bar.stop()
        self.progress_bar.grid_remove()
        self.protect_btn.configure(state="normal")
        
        self.status_label.configure(
            text=f"خطأ في الحماية: {error_message}",
            text_color=DARK_THEME['error_color']
        )
