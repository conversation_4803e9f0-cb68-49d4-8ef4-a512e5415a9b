# 🔒 FolderGuard - نظام حماية المجلدات المتقدم

نظام حماية متقدم للمجلدات باستخدام أحدث تقنيات الأمان والتشفير مع واجهة رسومية عصرية.

## ✨ المميزات الرئيسية

### 🛡️ الأمان والتشفير
- **تشفير AES-256** مع مفاتيح مشتقة باستخدام PBKDF2
- **المصادقة الثنائية (2FA)** باستخدام رموز TOTP
- **حماية من هجمات Brute-force** مع قفل الحساب
- **دعم مفاتيح USB** للحماية الإضافية
- **تسجيل مشفر** للأحداث مع التوقيع الرقمي

### 🎨 الواجهة الرسومية
- **تصميم عصري** بنمط مظلم
- **دعم السحب والإفلات** للمجلدات
- **أيقونات SVG** عالية الجودة
- **واجهة سهلة الاستخدام** باللغة العربية

### ⚙️ المميزات المتقدمة
- **خدمة خلفية** للمراقبة المستمرة
- **قفل تلقائي** عند عدم النشاط
- **إخفاء المجلدات** المحمية
- **نسخ احتياطية** قبل الحماية
- **مراقبة النظام** والموارد

## 📋 المتطلبات

### متطلبات النظام
- **Windows 10/11** أو **Linux** أو **macOS**
- **Python 3.8+**
- **4 GB RAM** (الحد الأدنى)
- **100 MB** مساحة فارغة

### المكتبات المطلوبة
```
cryptography>=41.0.0
customtkinter>=5.2.0
pyotp>=2.9.0
qrcode[pil]>=7.4.0
bcrypt>=4.1.0
Pillow>=10.0.0
pywin32>=306 (Windows only)
psutil>=5.9.0
```

## 🚀 التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone https://github.com/your-repo/folderguard.git
cd folderguard
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل البرنامج

#### تشغيل الواجهة الرسومية (افتراضي)
```bash
python main.py
```

#### تشغيل كخدمة خلفية
```bash
python main.py --service
```

#### استخدام سطر الأوامر
```bash
# عرض الحالة
python main.py --cli status

# حماية مجلد
python main.py --cli protect --folder "/path/to/folder" --password "mypassword"

# عرض المجلدات المحمية
python main.py --cli list

# عرض السجلات
python main.py --cli logs --lines 100
```

## 🔐 الاستخدام

### تسجيل الدخول الأول
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`
- ⚠️ **مهم:** قم بتغيير كلمة المرور فوراً بعد أول تسجيل دخول!

### حماية مجلد
1. اسحب المجلد إلى منطقة الإسقاط أو انقر "اختيار مجلد"
2. أدخل كلمة مرور قوية
3. اختر مستوى التشفير والخيارات المتقدمة
4. انقر "تطبيق الحماية"

### إلغاء حماية مجلد
1. اختر المجلد من قائمة المجلدات المحمية
2. انقر زر "إلغاء الحماية" 🔓
3. أدخل كلمة المرور
4. أدخل رمز 2FA إذا كان مفعلاً

### إعداد المصادقة الثنائية
1. انقر "إعداد المصادقة الثنائية" في نافذة تسجيل الدخول
2. امسح رمز QR باستخدام Google Authenticator
3. أدخل الرمز المكون من 6 أرقام للتأكيد

## 📁 هيكل المشروع

```
folderguard/
├── main.py                 # نقطة البداية الرئيسية
├── requirements.txt        # المتطلبات
├── README.md              # هذا الملف
├── core/                  # الوحدات الأساسية
│   ├── __init__.py
│   ├── security.py        # نظام التشفير والأمان
│   ├── auth.py           # المصادقة والجلسات
│   ├── folder_manager.py # إدارة المجلدات
│   ├── logger.py         # التسجيل المشفر
│   └── service.py        # خدمة الخلفية
├── gui/                   # الواجهة الرسومية
│   ├── __init__.py
│   ├── main_window.py    # النافذة الرئيسية
│   ├── login_dialog.py   # نافذة تسجيل الدخول
│   ├── protection_dialog.py # نافذة إعدادات الحماية
│   └── styles.py         # الأنماط والثيمات
└── utils/                 # الأدوات المساعدة
    ├── __init__.py
    ├── config.py         # إدارة الإعدادات
    └── database.py       # قاعدة البيانات
```

## ⚙️ الإعدادات

### ملف الإعدادات
يتم حفظ الإعدادات في:
- **Windows:** `%APPDATA%/FolderGuard/config.ini`
- **Linux/macOS:** `~/.folderguard/config.ini`

### الإعدادات الرئيسية
```ini
[Security]
max_login_attempts = 5
lockout_duration = 300
require_2fa = true
require_usb_key = false
auto_lock_timeout = 1800

[Encryption]
algorithm = AES-256-CBC
key_iterations = 100000

[GUI]
theme = dark
language = en
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة

#### خطأ في تثبيت المكتبات
```bash
# تحديث pip
python -m pip install --upgrade pip

# تثبيت المتطلبات مع إجبار التحديث
pip install -r requirements.txt --upgrade --force-reinstall
```

#### مشكلة في الأذونات (Linux/macOS)
```bash
# إعطاء أذونات التنفيذ
chmod +x main.py

# تشغيل مع sudo إذا لزم الأمر
sudo python main.py
```

#### مشكلة في CustomTkinter
```bash
# تثبيت إصدار محدد
pip install customtkinter==5.2.0
```

### ملفات السجل
- **سجل التطبيق:** `%APPDATA%/FolderGuard/logs/`
- **سجل الأمان:** `security.log` (مشفر)

## 🛡️ الأمان

### أفضل الممارسات
1. **استخدم كلمات مرور قوية** (8+ أحرف، أرقام، رموز)
2. **فعّل المصادقة الثنائية** لحماية إضافية
3. **احتفظ بنسخ احتياطية** من المفاتيح المهمة
4. **حدّث البرنامج** بانتظام
5. **راقب سجلات الأمان** للأنشطة المشبوهة

### تحذيرات أمنية
- ⚠️ لا تشارك كلمات المرور مع أحد
- ⚠️ لا تحفظ كلمات المرور في ملفات نصية
- ⚠️ تأكد من أمان جهازك من البرمجيات الخبيثة

## 📞 الدعم والمساعدة

### الإبلاغ عن مشاكل
إذا واجهت أي مشاكل، يرجى:
1. التحقق من قسم استكشاف الأخطاء
2. مراجعة ملفات السجل
3. إنشاء تقرير مشكلة مع التفاصيل

### المساهمة
نرحب بالمساهمات! يرجى:
1. Fork المشروع
2. إنشاء branch جديد للميزة
3. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للتفاصيل.

## 🙏 شكر وتقدير

- **Cryptography Library** للتشفير المتقدم
- **CustomTkinter** للواجهة الرسومية العصرية
- **PyOTP** للمصادقة الثنائية
- **مجتمع Python** للدعم والأدوات

---

**FolderGuard** - حماية متقدمة لملفاتك المهمة 🔒
