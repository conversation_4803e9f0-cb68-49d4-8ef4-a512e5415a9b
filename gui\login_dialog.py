"""
Modern Login Dialog with 2FA Support
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
import threading
from typing import Optional, Callable
from .styles import ModernStyles, DARK_THEME, FONTS
import qrcode
from PIL import Image, ImageTk
import io


class LoginDialog(ctk.CTkToplevel):
    """Modern login dialog with 2FA support"""
    
    def __init__(self, parent, auth_callback: Callable = None):
        super().__init__(parent)
        
        self.auth_callback = auth_callback
        self.result = None
        self.setup_complete = False
        
        self._setup_window()
        self._create_widgets()
        self._setup_bindings()
        
        # Center the window
        self.center_window()
        
        # Make modal
        self.transient(parent)
        self.grab_set()
        
        # Focus on username entry
        self.username_entry.focus()
    
    def _setup_window(self):
        """Setup window properties"""
        self.title("FolderGuard - تسجيل الدخول")
        self.geometry("450x600")
        self.resizable(False, False)
        
        # Configure grid
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)
        
        # Apply theme
        ModernStyles.configure_theme()
    
    def _create_widgets(self):
        """Create and layout widgets"""
        # Main container
        self.main_frame = ctk.CTkFrame(self, **ModernStyles.get_frame_style('card'))
        self.main_frame.grid(row=0, column=0, sticky="nsew", padx=20, pady=20)
        self.main_frame.grid_columnconfigure(0, weight=1)
        
        # Title
        self.title_label = ctk.CTkLabel(
            self.main_frame,
            text="🔒 FolderGuard",
            **ModernStyles.get_label_style('title')
        )
        self.title_label.grid(row=0, column=0, pady=(30, 10))
        
        # Subtitle
        self.subtitle_label = ctk.CTkLabel(
            self.main_frame,
            text="حماية متقدمة للمجلدات",
            **ModernStyles.get_label_style('small')
        )
        self.subtitle_label.grid(row=1, column=0, pady=(0, 30))
        
        # Login form frame
        self.form_frame = ctk.CTkFrame(self.main_frame, fg_color="transparent")
        self.form_frame.grid(row=2, column=0, sticky="ew", padx=30)
        self.form_frame.grid_columnconfigure(0, weight=1)
        
        # Username field
        self.username_label = ctk.CTkLabel(
            self.form_frame,
            text="اسم المستخدم:",
            **ModernStyles.get_label_style()
        )
        self.username_label.grid(row=0, column=0, sticky="w", pady=(0, 5))
        
        self.username_entry = ctk.CTkEntry(
            self.form_frame,
            placeholder_text="أدخل اسم المستخدم",
            **ModernStyles.get_entry_style()
        )
        self.username_entry.grid(row=1, column=0, sticky="ew", pady=(0, 15))
        
        # Password field
        self.password_label = ctk.CTkLabel(
            self.form_frame,
            text="كلمة المرور:",
            **ModernStyles.get_label_style()
        )
        self.password_label.grid(row=2, column=0, sticky="w", pady=(0, 5))
        
        self.password_entry = ctk.CTkEntry(
            self.form_frame,
            placeholder_text="أدخل كلمة المرور",
            show="*",
            **ModernStyles.get_entry_style()
        )
        self.password_entry.grid(row=3, column=0, sticky="ew", pady=(0, 15))
        
        # 2FA field (initially hidden)
        self.totp_label = ctk.CTkLabel(
            self.form_frame,
            text="رمز التحقق الثنائي:",
            **ModernStyles.get_label_style()
        )
        
        self.totp_entry = ctk.CTkEntry(
            self.form_frame,
            placeholder_text="أدخل الرمز المكون من 6 أرقام",
            **ModernStyles.get_entry_style()
        )
        
        # USB Key status
        self.usb_status_frame = ctk.CTkFrame(self.form_frame, fg_color="transparent")
        self.usb_status_frame.grid(row=6, column=0, sticky="ew", pady=(10, 15))
        self.usb_status_frame.grid_columnconfigure(1, weight=1)
        
        self.usb_icon_label = ctk.CTkLabel(
            self.usb_status_frame,
            text="🔑",
            font=FONTS['heading']
        )
        self.usb_icon_label.grid(row=0, column=0, padx=(0, 10))
        
        self.usb_status_label = ctk.CTkLabel(
            self.usb_status_frame,
            text="البحث عن مفتاح USB...",
            **ModernStyles.get_label_style('small')
        )
        self.usb_status_label.grid(row=0, column=1, sticky="w")
        
        # Login button
        self.login_button = ctk.CTkButton(
            self.form_frame,
            text="تسجيل الدخول",
            command=self._handle_login,
            **ModernStyles.get_button_style('primary')
        )
        self.login_button.grid(row=7, column=0, sticky="ew", pady=(0, 15))
        
        # Setup 2FA button
        self.setup_2fa_button = ctk.CTkButton(
            self.form_frame,
            text="إعداد المصادقة الثنائية",
            command=self._show_2fa_setup,
            **ModernStyles.get_button_style('secondary')
        )
        self.setup_2fa_button.grid(row=8, column=0, sticky="ew", pady=(0, 10))
        
        # Status label
        self.status_label = ctk.CTkLabel(
            self.main_frame,
            text="",
            **ModernStyles.get_label_style('small')
        )
        self.status_label.grid(row=3, column=0, pady=(10, 20))
        
        # Progress bar
        self.progress_bar = ctk.CTkProgressBar(
            self.main_frame,
            **ModernStyles.get_progressbar_style()
        )
        self.progress_bar.grid(row=4, column=0, sticky="ew", padx=30, pady=(0, 20))
        self.progress_bar.set(0)
        self.progress_bar.grid_remove()  # Hide initially
    
    def _setup_bindings(self):
        """Setup keyboard bindings"""
        self.bind('<Return>', lambda e: self._handle_login())
        self.bind('<Escape>', lambda e: self.destroy())
        
        # Tab navigation
        self.username_entry.bind('<Tab>', lambda e: self.password_entry.focus())
        self.password_entry.bind('<Tab>', lambda e: self.totp_entry.focus() if self.totp_entry.winfo_viewable() else None)
    
    def center_window(self):
        """Center the window on screen"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f"{width}x{height}+{x}+{y}")
    
    def show_2fa_field(self, show: bool = True):
        """Show or hide 2FA field"""
        if show:
            self.totp_label.grid(row=4, column=0, sticky="w", pady=(0, 5))
            self.totp_entry.grid(row=5, column=0, sticky="ew", pady=(0, 15))
            self.totp_entry.focus()
        else:
            self.totp_label.grid_remove()
            self.totp_entry.grid_remove()
    
    def update_usb_status(self, status: str, found: bool = False):
        """Update USB key status"""
        self.usb_status_label.configure(text=status)
        if found:
            self.usb_icon_label.configure(text="🔑✅")
            self.usb_status_label.configure(text_color=DARK_THEME['success_color'])
        else:
            self.usb_icon_label.configure(text="🔑❌")
            self.usb_status_label.configure(text_color=DARK_THEME['error_color'])
    
    def show_progress(self, show: bool = True):
        """Show or hide progress bar"""
        if show:
            self.progress_bar.grid()
            self.progress_bar.start()
        else:
            self.progress_bar.stop()
            self.progress_bar.grid_remove()
    
    def update_status(self, message: str, status_type: str = 'info'):
        """Update status message"""
        color_map = {
            'info': DARK_THEME['info_color'],
            'success': DARK_THEME['success_color'],
            'warning': DARK_THEME['warning_color'],
            'error': DARK_THEME['error_color']
        }
        
        self.status_label.configure(
            text=message,
            text_color=color_map.get(status_type, DARK_THEME['text_color'])
        )
    
    def _handle_login(self):
        """Handle login attempt"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get()
        totp_code = self.totp_entry.get().strip() if self.totp_entry.winfo_viewable() else ""
        
        if not username or not password:
            self.update_status("يرجى إدخال اسم المستخدم وكلمة المرور", 'error')
            return
        
        # Disable login button and show progress
        self.login_button.configure(state="disabled")
        self.show_progress(True)
        self.update_status("جاري التحقق من البيانات...")
        
        # Perform authentication in background thread
        def authenticate():
            try:
                if self.auth_callback:
                    result = self.auth_callback(username, password, totp_code)
                    self.after(0, lambda: self._handle_auth_result(result))
                else:
                    # Mock authentication for testing
                    import time
                    time.sleep(2)
                    self.after(0, lambda: self._handle_auth_result({'success': True, 'user_id': 1}))
            except Exception as e:
                self.after(0, lambda: self._handle_auth_result({'success': False, 'error': str(e)}))
        
        threading.Thread(target=authenticate, daemon=True).start()
    
    def _handle_auth_result(self, result: dict):
        """Handle authentication result"""
        self.show_progress(False)
        self.login_button.configure(state="normal")
        
        if result.get('success'):
            self.update_status("تم تسجيل الدخول بنجاح!", 'success')
            self.result = result
            self.after(1000, self.destroy)  # Close after 1 second
        else:
            error_msg = result.get('error', 'فشل في تسجيل الدخول')
            
            if 'require_2fa' in result:
                self.show_2fa_field(True)
                self.update_status("يرجى إدخال رمز التحقق الثنائي", 'warning')
            elif 'locked' in result:
                self.update_status("تم قفل الحساب مؤقتاً", 'error')
                self.login_button.configure(state="disabled")
            else:
                self.update_status(error_msg, 'error')
    
    def _show_2fa_setup(self):
        """Show 2FA setup dialog"""
        setup_dialog = TwoFactorSetupDialog(self)
        setup_dialog.wait_window()
        
        if setup_dialog.result:
            self.update_status("تم إعداد المصادقة الثنائية بنجاح!", 'success')


class TwoFactorSetupDialog(ctk.CTkToplevel):
    """Dialog for setting up 2FA"""
    
    def __init__(self, parent):
        super().__init__(parent)
        
        self.result = None
        self.qr_image = None
        
        self._setup_window()
        self._create_widgets()
        
        # Center and make modal
        self.center_window()
        self.transient(parent)
        self.grab_set()
    
    def _setup_window(self):
        """Setup window properties"""
        self.title("إعداد المصادقة الثنائية")
        self.geometry("500x700")
        self.resizable(False, False)
        
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)
    
    def _create_widgets(self):
        """Create setup widgets"""
        # Main frame
        self.main_frame = ctk.CTkFrame(self, **ModernStyles.get_frame_style('card'))
        self.main_frame.grid(row=0, column=0, sticky="nsew", padx=20, pady=20)
        self.main_frame.grid_columnconfigure(0, weight=1)
        
        # Title
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="🔐 إعداد المصادقة الثنائية",
            **ModernStyles.get_label_style('heading')
        )
        title_label.grid(row=0, column=0, pady=(20, 10))
        
        # Instructions
        instructions = ctk.CTkLabel(
            self.main_frame,
            text="1. قم بتحميل تطبيق Google Authenticator\n2. امسح الرمز أدناه\n3. أدخل الرمز المكون من 6 أرقام",
            **ModernStyles.get_label_style('small'),
            justify="right"
        )
        instructions.grid(row=1, column=0, pady=(0, 20))
        
        # QR Code placeholder
        self.qr_label = ctk.CTkLabel(
            self.main_frame,
            text="جاري إنشاء رمز QR...",
            width=200,
            height=200,
            **ModernStyles.get_label_style()
        )
        self.qr_label.grid(row=2, column=0, pady=20)
        
        # Secret key
        self.secret_label = ctk.CTkLabel(
            self.main_frame,
            text="المفتاح السري (للإدخال اليدوي):",
            **ModernStyles.get_label_style('small')
        )
        self.secret_label.grid(row=3, column=0, pady=(10, 5))
        
        self.secret_entry = ctk.CTkEntry(
            self.main_frame,
            state="readonly",
            **ModernStyles.get_entry_style()
        )
        self.secret_entry.grid(row=4, column=0, sticky="ew", padx=20, pady=(0, 20))
        
        # Verification code
        verify_label = ctk.CTkLabel(
            self.main_frame,
            text="أدخل رمز التحقق:",
            **ModernStyles.get_label_style()
        )
        verify_label.grid(row=5, column=0, pady=(0, 5))
        
        self.verify_entry = ctk.CTkEntry(
            self.main_frame,
            placeholder_text="123456",
            **ModernStyles.get_entry_style()
        )
        self.verify_entry.grid(row=6, column=0, sticky="ew", padx=20, pady=(0, 20))
        
        # Buttons
        button_frame = ctk.CTkFrame(self.main_frame, fg_color="transparent")
        button_frame.grid(row=7, column=0, sticky="ew", padx=20, pady=(0, 20))
        button_frame.grid_columnconfigure((0, 1), weight=1)
        
        cancel_button = ctk.CTkButton(
            button_frame,
            text="إلغاء",
            command=self.destroy,
            **ModernStyles.get_button_style('secondary')
        )
        cancel_button.grid(row=0, column=0, sticky="ew", padx=(0, 10))
        
        confirm_button = ctk.CTkButton(
            button_frame,
            text="تأكيد",
            command=self._confirm_setup,
            **ModernStyles.get_button_style('primary')
        )
        confirm_button.grid(row=0, column=1, sticky="ew", padx=(10, 0))
        
        # Generate QR code
        self._generate_qr_code()
    
    def center_window(self):
        """Center the window"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f"{width}x{height}+{x}+{y}")
    
    def _generate_qr_code(self):
        """Generate QR code for 2FA setup"""
        # Mock secret for demo
        secret = "JBSWY3DPEHPK3PXP"
        
        # Update secret entry
        self.secret_entry.configure(state="normal")
        self.secret_entry.delete(0, tk.END)
        self.secret_entry.insert(0, secret)
        self.secret_entry.configure(state="readonly")
        
        # Generate QR code
        try:
            import pyotp
            totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
                name="user@folderguard",
                issuer_name="FolderGuard"
            )
            
            qr = qrcode.QRCode(version=1, box_size=8, border=4)
            qr.add_data(totp_uri)
            qr.make(fit=True)
            
            qr_img = qr.make_image(fill_color="white", back_color="black")
            qr_img = qr_img.resize((200, 200))
            
            # Convert to PhotoImage
            self.qr_image = ImageTk.PhotoImage(qr_img)
            self.qr_label.configure(image=self.qr_image, text="")
            
        except Exception as e:
            self.qr_label.configure(text=f"خطأ في إنشاء QR: {e}")
    
    def _confirm_setup(self):
        """Confirm 2FA setup"""
        code = self.verify_entry.get().strip()
        
        if len(code) != 6 or not code.isdigit():
            messagebox.showerror("خطأ", "يرجى إدخال رمز صحيح مكون من 6 أرقام")
            return
        
        # Verify code (mock verification)
        self.result = {'secret': self.secret_entry.get(), 'verified': True}
        self.destroy()
