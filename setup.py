#!/usr/bin/env python3
"""
FolderGuard Setup Script
Automated setup and installation
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ is required")
        print(f"   Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} detected")
    return True


def install_requirements():
    """Install required packages"""
    print("📦 Installing requirements...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False


def create_desktop_shortcut():
    """Create desktop shortcut (Windows only)"""
    if platform.system() != "Windows":
        return True
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "FolderGuard.lnk")
        target = os.path.join(os.getcwd(), "run.bat")
        wDir = os.getcwd()
        icon = target
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = target
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = icon
        shortcut.save()
        
        print("✅ Desktop shortcut created")
        return True
    except Exception as e:
        print(f"⚠️  Could not create desktop shortcut: {e}")
        return True  # Not critical


def setup_directories():
    """Setup required directories"""
    print("📁 Setting up directories...")
    
    directories = [
        "logs",
        "temp", 
        "backups",
        "assets"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    print("✅ Directories created")
    return True


def test_imports():
    """Test if all required modules can be imported"""
    print("🧪 Testing imports...")
    
    required_modules = [
        "customtkinter",
        "cryptography", 
        "pyotp",
        "qrcode",
        "bcrypt",
        "PIL",
        "psutil"
    ]
    
    failed_imports = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"   ✅ {module}")
        except ImportError:
            print(f"   ❌ {module}")
            failed_imports.append(module)
    
    if failed_imports:
        print(f"\n❌ Failed to import: {', '.join(failed_imports)}")
        print("   Try running: pip install -r requirements.txt")
        return False
    
    print("✅ All modules imported successfully")
    return True


def create_sample_config():
    """Create sample configuration file"""
    print("⚙️  Creating sample configuration...")
    
    config_content = """# FolderGuard Configuration
# This is a sample configuration file
# The actual configuration will be created automatically

[Security]
max_login_attempts = 5
lockout_duration = 300
require_2fa = true
require_usb_key = false

[GUI]
theme = dark
language = en
window_width = 1200
window_height = 800

[Encryption]
algorithm = AES-256-CBC
key_iterations = 100000
"""
    
    try:
        with open("config.sample.ini", "w") as f:
            f.write(config_content)
        print("✅ Sample configuration created")
        return True
    except Exception as e:
        print(f"⚠️  Could not create sample config: {e}")
        return True  # Not critical


def main():
    """Main setup function"""
    print("🔒 FolderGuard Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Setup directories
    if not setup_directories():
        return False
    
    # Install requirements
    if not install_requirements():
        return False
    
    # Test imports
    if not test_imports():
        return False
    
    # Create sample config
    create_sample_config()
    
    # Create desktop shortcut (Windows only)
    create_desktop_shortcut()
    
    print("\n🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Run the application:")
    if platform.system() == "Windows":
        print("   - Double-click 'run.bat'")
        print("   - Or run: python main.py")
    else:
        print("   - Run: ./run.sh")
        print("   - Or run: python3 main.py")
    
    print("\n2. Default login credentials:")
    print("   Username: admin")
    print("   Password: admin123")
    print("   ⚠️  Change the password after first login!")
    
    print("\n3. For help, check README.md")
    
    return True


if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n🛑 Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        sys.exit(1)
