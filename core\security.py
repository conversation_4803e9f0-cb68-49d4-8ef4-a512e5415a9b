"""
Advanced Security Module for Folder Protection
Implements AES-256 encryption with PBKDF2 key derivation
"""

import os
import secrets
import hashlib
import base64
from typing import Op<PERSON>, Tuple
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.backends import default_backend
import bcrypt


class SecurityManager:
    """Advanced security manager with AES-256 encryption and secure key derivation"""
    
    def __init__(self):
        self.backend = default_backend()
        self.key_iterations = 100000  # PBKDF2 iterations
        self.salt_length = 32
        self.iv_length = 16
        
    def generate_salt(self) -> bytes:
        """Generate cryptographically secure salt"""
        return secrets.token_bytes(self.salt_length)
    
    def generate_iv(self) -> bytes:
        """Generate cryptographically secure initialization vector"""
        return secrets.token_bytes(self.iv_length)
    
    def derive_key(self, password: str, salt: bytes) -> bytes:
        """Derive encryption key using PBKDF2"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,  # 256 bits for AES-256
            salt=salt,
            iterations=self.key_iterations,
            backend=self.backend
        )
        return kdf.derive(password.encode('utf-8'))
    
    def encrypt_data(self, data: bytes, password: str) -> Tuple[bytes, bytes, bytes]:
        """
        Encrypt data using AES-256-CBC
        Returns: (encrypted_data, salt, iv)
        """
        salt = self.generate_salt()
        iv = self.generate_iv()
        key = self.derive_key(password, salt)
        
        cipher = Cipher(
            algorithms.AES(key),
            modes.CBC(iv),
            backend=self.backend
        )
        encryptor = cipher.encryptor()
        
        # Add PKCS7 padding
        padded_data = self._add_padding(data)
        encrypted_data = encryptor.update(padded_data) + encryptor.finalize()
        
        return encrypted_data, salt, iv
    
    def decrypt_data(self, encrypted_data: bytes, password: str, salt: bytes, iv: bytes) -> bytes:
        """Decrypt data using AES-256-CBC"""
        key = self.derive_key(password, salt)
        
        cipher = Cipher(
            algorithms.AES(key),
            modes.CBC(iv),
            backend=self.backend
        )
        decryptor = cipher.decryptor()
        
        padded_data = decryptor.update(encrypted_data) + decryptor.finalize()
        return self._remove_padding(padded_data)
    
    def _add_padding(self, data: bytes) -> bytes:
        """Add PKCS7 padding"""
        padding_length = 16 - (len(data) % 16)
        padding = bytes([padding_length] * padding_length)
        return data + padding
    
    def _remove_padding(self, padded_data: bytes) -> bytes:
        """Remove PKCS7 padding"""
        padding_length = padded_data[-1]
        return padded_data[:-padding_length]
    
    def hash_password(self, password: str) -> str:
        """Hash password using bcrypt"""
        salt = bcrypt.gensalt(rounds=12)
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        return base64.b64encode(hashed).decode('utf-8')
    
    def verify_password(self, password: str, hashed_password: str) -> bool:
        """Verify password against bcrypt hash"""
        try:
            hashed_bytes = base64.b64decode(hashed_password.encode('utf-8'))
            return bcrypt.checkpw(password.encode('utf-8'), hashed_bytes)
        except Exception:
            return False
    
    def generate_secure_token(self, length: int = 32) -> str:
        """Generate cryptographically secure token"""
        return secrets.token_urlsafe(length)
    
    def encrypt_file(self, file_path: str, password: str) -> bool:
        """Encrypt a file in place"""
        try:
            with open(file_path, 'rb') as f:
                data = f.read()
            
            encrypted_data, salt, iv = self.encrypt_data(data, password)
            
            # Create encrypted file with metadata
            with open(file_path + '.enc', 'wb') as f:
                f.write(salt)
                f.write(iv)
                f.write(encrypted_data)
            
            # Remove original file
            os.remove(file_path)
            return True
        except Exception as e:
            print(f"Encryption error: {e}")
            return False
    
    def decrypt_file(self, encrypted_file_path: str, password: str, output_path: str = None) -> bool:
        """Decrypt a file"""
        try:
            with open(encrypted_file_path, 'rb') as f:
                salt = f.read(self.salt_length)
                iv = f.read(self.iv_length)
                encrypted_data = f.read()
            
            decrypted_data = self.decrypt_data(encrypted_data, password, salt, iv)
            
            if output_path is None:
                output_path = encrypted_file_path.replace('.enc', '')
            
            with open(output_path, 'wb') as f:
                f.write(decrypted_data)
            
            return True
        except Exception as e:
            print(f"Decryption error: {e}")
            return False


class BruteForceProtection:
    """Protection against brute force attacks"""
    
    def __init__(self, max_attempts: int = 5, lockout_time: int = 300):
        self.max_attempts = max_attempts
        self.lockout_time = lockout_time  # seconds
        self.failed_attempts = {}
        self.lockout_times = {}
    
    def is_locked_out(self, identifier: str) -> bool:
        """Check if identifier is locked out"""
        if identifier in self.lockout_times:
            import time
            if time.time() - self.lockout_times[identifier] < self.lockout_time:
                return True
            else:
                # Lockout expired
                del self.lockout_times[identifier]
                if identifier in self.failed_attempts:
                    del self.failed_attempts[identifier]
        return False
    
    def record_failed_attempt(self, identifier: str) -> bool:
        """Record failed attempt, return True if locked out"""
        if identifier not in self.failed_attempts:
            self.failed_attempts[identifier] = 0
        
        self.failed_attempts[identifier] += 1
        
        if self.failed_attempts[identifier] >= self.max_attempts:
            import time
            self.lockout_times[identifier] = time.time()
            return True
        
        return False
    
    def reset_attempts(self, identifier: str):
        """Reset failed attempts for identifier"""
        if identifier in self.failed_attempts:
            del self.failed_attempts[identifier]
        if identifier in self.lockout_times:
            del self.lockout_times[identifier]
